import '/component/stepper/stepper_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/index.dart';
import 'signup_widget.dart' show SignupWidget;
import 'package:flutter/material.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class SignupModel extends FlutterFlowModel<SignupWidget> {
  ///  State fields for stateful widgets in this page.

  final formKey4 = GlobalKey<FormState>();
  final formKey2 = GlobalKey<FormState>();
  final formKey3 = GlobalKey<FormState>();
  final formKey1 = GlobalKey<FormState>();
  // State field(s) for EmailLogin widget.
  FocusNode? emailLoginFocusNode;
  TextEditingController? emailLoginTextController;
  String? Function(BuildContext, String?)? emailLoginTextControllerValidator;
  // State field(s) for Passwordlogin widget.
  FocusNode? passwordloginFocusNode;
  TextEditingController? passwordloginTextController;
  late bool passwordloginVisibility;
  String? Function(BuildContext, String?)? passwordloginTextControllerValidator;
  // State field(s) for ConfirmPasswordlogin widget.
  FocusNode? confirmPasswordloginFocusNode;
  TextEditingController? confirmPasswordloginTextController;
  late bool confirmPasswordloginVisibility;
  String? Function(BuildContext, String?)?
      confirmPasswordloginTextControllerValidator;
  // Model for Stepper component.
  late StepperModel stepperModel1;
  // State field(s) for FirstNameTextField widget.
  FocusNode? firstNameTextFieldFocusNode;
  TextEditingController? firstNameTextFieldTextController;
  String? Function(BuildContext, String?)?
      firstNameTextFieldTextControllerValidator;
  // State field(s) for LastNameTextField widget.
  FocusNode? lastNameTextFieldFocusNode;
  TextEditingController? lastNameTextFieldTextController;
  String? Function(BuildContext, String?)?
      lastNameTextFieldTextControllerValidator;
  // State field(s) for PhoneNumberTextField widget.
  FocusNode? phoneNumberTextFieldFocusNode;
  TextEditingController? phoneNumberTextFieldTextController;
  final phoneNumberTextFieldMask =
      MaskTextInputFormatter(mask: '(###) ###-####');
  String? Function(BuildContext, String?)?
      phoneNumberTextFieldTextControllerValidator;
  // State field(s) for CityTextField widget.
  FocusNode? cityTextFieldFocusNode;
  TextEditingController? cityTextFieldTextController;
  String? Function(BuildContext, String?)? cityTextFieldTextControllerValidator;
  // State field(s) for ZipCodeTextField widget.
  FocusNode? zipCodeTextFieldFocusNode;
  TextEditingController? zipCodeTextFieldTextController;
  String? Function(BuildContext, String?)?
      zipCodeTextFieldTextControllerValidator;
  // State field(s) for StateDropDown widget.
  String? stateDropDownValue;
  FormFieldController<String>? stateDropDownValueController;
  // State field(s) for Addressline1TextField widget.
  FocusNode? addressline1TextFieldFocusNode1;
  TextEditingController? addressline1TextFieldTextController1;
  String? Function(BuildContext, String?)?
      addressline1TextFieldTextController1Validator;
  // State field(s) for Addressline1TextField widget.
  FocusNode? addressline1TextFieldFocusNode2;
  TextEditingController? addressline1TextFieldTextController2;
  String? Function(BuildContext, String?)?
      addressline1TextFieldTextController2Validator;
  // Model for Stepper component.
  late StepperModel stepperModel2;
  // State field(s) for TicketTextField widget.
  FocusNode? ticketTextFieldFocusNode;
  TextEditingController? ticketTextFieldTextController;
  String? Function(BuildContext, String?)?
      ticketTextFieldTextControllerValidator;
  // State field(s) for LocalTextField widget.
  FocusNode? localTextFieldFocusNode;
  TextEditingController? localTextFieldTextController;
  String? Function(BuildContext, String?)?
      localTextFieldTextControllerValidator;
  // State field(s) for ClassificationeDropDown widget.
  String? classificationeDropDownValue;
  FormFieldController<String>? classificationeDropDownValueController;
  // State field(s) for BooksTextField widget.
  FocusNode? booksTextFieldFocusNode;
  TextEditingController? booksTextFieldTextController;
  String? Function(BuildContext, String?)?
      booksTextFieldTextControllerValidator;
  // State field(s) for PreferenceLocal1TextField widget.
  FocusNode? preferenceLocal1TextFieldFocusNode;
  TextEditingController? preferenceLocal1TextFieldTextController;
  String? Function(BuildContext, String?)?
      preferenceLocal1TextFieldTextControllerValidator;
  // State field(s) for PreferenceLocal2TextField widget.
  FocusNode? preferenceLocal2TextFieldFocusNode;
  TextEditingController? preferenceLocal2TextFieldTextController;
  String? Function(BuildContext, String?)?
      preferenceLocal2TextFieldTextControllerValidator;
  // State field(s) for PreferenceLocal3TextField widget.
  FocusNode? preferenceLocal3TextFieldFocusNode;
  TextEditingController? preferenceLocal3TextFieldTextController;
  String? Function(BuildContext, String?)?
      preferenceLocal3TextFieldTextControllerValidator;
  // State field(s) for ConstructionType multi-select.
  List<String> selectedConstructionTypes = [];
  // Model for Stepper component.
  late StepperModel stepperModel3;
  // State field(s) for Addressline1CareerGoals widget.
  FocusNode? addressline1CareerGoalsFocusNode;
  TextEditingController? addressline1CareerGoalsTextController;
  String? Function(BuildContext, String?)?
      addressline1CareerGoalsTextControllerValidator;
  // State field(s) for Addressline1AboutUs widget.
  FocusNode? addressline1AboutUsFocusNode;
  TextEditingController? addressline1AboutUsTextController;
  String? Function(BuildContext, String?)?
      addressline1AboutUsTextControllerValidator;
  // State field(s) for Addressline1LookingToAccomp widget.
  FocusNode? addressline1LookingToAccompFocusNode;
  TextEditingController? addressline1LookingToAccompTextController;
  String? Function(BuildContext, String?)?
      addressline1LookingToAccompTextControllerValidator;
  // State field(s) for SwitchLongTerm widget.
  bool? switchLongTermValue;
  // State field(s) for SwitchNewLocation widget.
  bool? switchNewLocationValue;
  // State field(s) for SwitchNewSkill widget.
  bool? switchNewSkillValue;
  // State field(s) for SwitchPayScale widget.
  bool? switchPayScaleValue;
  // State field(s) for SwitchBenefits widget.
  bool? switchBenefitsValue;
  // State field(s) for SwitcCareerAdvan widget.
  bool? switcCareerAdvanValue;
  // State field(s) for SwitchNetwork widget.
  bool? switchNetworkValue;

  @override
  void initState(BuildContext context) {
    passwordloginVisibility = false;
    confirmPasswordloginVisibility = false;
    stepperModel1 = createModel(context, () => StepperModel());
    stepperModel2 = createModel(context, () => StepperModel());
    stepperModel3 = createModel(context, () => StepperModel());
  }

  @override
  void dispose() {
    emailLoginFocusNode?.dispose();
    emailLoginTextController?.dispose();

    passwordloginFocusNode?.dispose();
    passwordloginTextController?.dispose();

    confirmPasswordloginFocusNode?.dispose();
    confirmPasswordloginTextController?.dispose();

    stepperModel1.dispose();
    firstNameTextFieldFocusNode?.dispose();
    firstNameTextFieldTextController?.dispose();

    lastNameTextFieldFocusNode?.dispose();
    lastNameTextFieldTextController?.dispose();

    phoneNumberTextFieldFocusNode?.dispose();
    phoneNumberTextFieldTextController?.dispose();

    cityTextFieldFocusNode?.dispose();
    cityTextFieldTextController?.dispose();

    zipCodeTextFieldFocusNode?.dispose();
    zipCodeTextFieldTextController?.dispose();

    addressline1TextFieldFocusNode1?.dispose();
    addressline1TextFieldTextController1?.dispose();

    addressline1TextFieldFocusNode2?.dispose();
    addressline1TextFieldTextController2?.dispose();

    stepperModel2.dispose();
    ticketTextFieldFocusNode?.dispose();
    ticketTextFieldTextController?.dispose();

    localTextFieldFocusNode?.dispose();
    localTextFieldTextController?.dispose();

    booksTextFieldFocusNode?.dispose();
    booksTextFieldTextController?.dispose();

    preferenceLocal1TextFieldFocusNode?.dispose();
    preferenceLocal1TextFieldTextController?.dispose();

    preferenceLocal2TextFieldFocusNode?.dispose();
    preferenceLocal2TextFieldTextController?.dispose();

    preferenceLocal3TextFieldFocusNode?.dispose();
    preferenceLocal3TextFieldTextController?.dispose();

    stepperModel3.dispose();
    addressline1CareerGoalsFocusNode?.dispose();
    addressline1CareerGoalsTextController?.dispose();

    addressline1AboutUsFocusNode?.dispose();
    addressline1AboutUsTextController?.dispose();

    addressline1LookingToAccompFocusNode?.dispose();
    addressline1LookingToAccompTextController?.dispose();
  }
}
