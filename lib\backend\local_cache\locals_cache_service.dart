import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

// Define a callback type for progress updates
typedef CacheProgressCallback = void Function(double progress, String message);

class LocalsCacheService {
  static const String _dbName = 'locals_cache.db';
  static const String _tableName = 'locals_data';
  static const String _columnId = 'id';
  static const String _columnData = 'data';
  static const String _columnTimestamp = 'timestamp';
  static const int _cacheDurationHours = 24; // Cache data for 24 hours

  Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, _dbName);
    return openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE $_tableName (
            $_columnId INTEGER PRIMARY KEY AUTOINCREMENT,
            $_columnData TEXT NOT NULL,
            $_columnTimestamp INTEGER NOT NULL
          )
        ''');
      },
    );
  }

  Future<void> cacheLocalsData(Map<String, dynamic> data, {CacheProgressCallback? onProgress}) async {
    final db = await database;
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    onProgress?.call(0.1, 'Encoding data...');
    final String jsonData = jsonEncode(data);
    onProgress?.call(0.3, 'Data encoded. Caching...');

    // Clear old cache before inserting new data
    await db.delete(_tableName);
    onProgress?.call(0.5, 'Old cache cleared.');

    await db.insert(
      _tableName,
      {
        _columnData: jsonData,
        _columnTimestamp: timestamp,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    onProgress?.call(1.0, 'Data cached successfully.');
  }

  Future<Map<String, dynamic>?> getCachedLocalsData({CacheProgressCallback? onProgress}) async {
    final db = await database;
    onProgress?.call(0.1, 'Accessing cache...');

    final List<Map<String, dynamic>> maps = await db.query(_tableName);

    if (maps.isNotEmpty) {
      final cachedItem = maps.first;
      final timestamp = cachedItem[_columnTimestamp] as int;
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final currentTime = DateTime.now();

      onProgress?.call(0.5, 'Cache found. Checking freshness...');
      if (currentTime.difference(cacheTime).inHours < _cacheDurationHours) {
        onProgress?.call(0.8, 'Cache is fresh. Decoding data...');
        final String jsonData = cachedItem[_columnData] as String;
        final Map<String, dynamic> data = jsonDecode(jsonData) as Map<String, dynamic>;
        onProgress?.call(1.0, 'Data retrieved from cache.');
        return data;
      } else {
        onProgress?.call(0.8, 'Cache is stale. Clearing...');
        await db.delete(_tableName); // Clear stale cache
        onProgress?.call(1.0, 'Stale cache cleared.');
      }
    } else {
      onProgress?.call(0.5, 'No data in cache.');
    }
    return null;
  }

  Future<bool> isCacheValid() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_tableName);
    if (maps.isNotEmpty) {
      final timestamp = maps.first[_columnTimestamp] as int;
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final currentTime = DateTime.now();
      return currentTime.difference(cacheTime).inHours < _cacheDurationHours;
    }
    return false;
  }

  Future<void> clearCache() async {
    final db = await database;
    await db.delete(_tableName);
  }
}

class LocalsDataRepository {
  final LocalsCacheService _cacheService = LocalsCacheService();
  // Placeholder for network service - replace with actual implementation
  final NetworkService _networkService = NetworkService();

  Stream<Map<String, dynamic>> getLocalsData({
    bool forceRefresh = false,
    CacheProgressCallback? onProgress,
  }) async* {
    onProgress?.call(0.0, 'Starting data load process...');

    if (!forceRefresh) {
      onProgress?.call(0.1, 'Checking cache...');
      final cachedData = await _cacheService.getCachedLocalsData(onProgress: (progress, message) {
        // Adjust progress to fit within the 0.1 to 0.4 range for cache check
        onProgress?.call(0.1 + (progress * 0.3), message);
      });
      if (cachedData != null) {
        onProgress?.call(1.0, 'Data loaded from cache.');
        yield cachedData;
        return;
      }
    }

    onProgress?.call(0.4, 'Cache is empty or refresh forced. Fetching from network...');
    try {
      // Simulate network delay and progress
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network latency
      onProgress?.call(0.6, 'Fetching data from server...');
      final networkData = await _networkService.fetchLocalsData(); // Replace with actual network call
      
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate processing delay
      onProgress?.call(0.8, 'Data received. Caching data...');
      await _cacheService.cacheLocalsData(networkData, onProgress: (progress, message) {
         // Adjust progress to fit within the 0.8 to 0.99 range for caching
        onProgress?.call(0.8 + (progress * 0.19), message);
      });
      onProgress?.call(1.0, 'Data fetched and cached successfully.');
      yield networkData;
    } catch (e) {
      onProgress?.call(1.0, 'Error fetching data: ${e.toString()}');
      // Consider re-throwing the error or handling it as per app's error strategy
      throw Exception('Failed to load locals data: ${e.toString()}');
    }
  }

  // Background prefetching logic
  Future<void> prefetchLocalsDataInBackground() async {
    // No progress reporting for background tasks, or use a different mechanism (e.g., silent logs)
    print('Starting background prefetch of locals data...');
    try {
      final isCacheStillValid = await _cacheService.isCacheValid();
      if (!isCacheStillValid) {
        print('Cache is invalid or empty. Prefetching from network...');
        final networkData = await _networkService.fetchLocalsData();
        await _cacheService.cacheLocalsData(networkData);
        print('Locals data prefetched and cached successfully.');
      } else {
        print('Cache is still valid. No prefetch needed.');
      }
    } catch (e) {
      print('Error during background prefetch: ${e.toString()}');
      // Handle error silently or log for debugging
    }
  }
}

// Placeholder NetworkService. Replace with your actual network implementation.
class NetworkService {
  Future<Map<String, dynamic>> fetchLocalsData() async {
    // Simulate a network request
    await Future.delayed(const Duration(seconds: 2));
    // This should be your actual API call to fetch the large JSON
    // For demonstration, returning a sample map.
    // In a real scenario, this would fetch the 13,000+ line JSON.
    print("Fetching data from network..."); // Log network activity
    return Future.value({
      "sampleData": List.generate(100, (index) => {"id": index, "name": "Local $index", "details": "Details for local $index"})
    });
  }
}