import 'package:flutter/material.dart';
import '../../utils/animation_utils.dart';
import '../../backend/schema/jobs_record.dart';

/// Displays job details in a bottom sheet with animation.
Future<void> showJobDetailsSheet(BuildContext context, JobsRecord job) {
  return showGeneralDialog(
    barrierLabel: 'Job Details',
    barrierDismissible: true,
    barrierColor: Colors.black54,
    transitionDuration: AnimationDurations.medium,
    context: context,
    pageBuilder: (context, animation, secondaryAnimation) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: JobDetailsSheet(job: job),
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return AnimationUtils.slideTransitionVertical(
        child,
        animation,
        reverse: true,
        offset: 1.0,
      );
    },
  );
}

class JobDetailsSheet extends StatelessWidget {
  final JobsRecord job;

  const JobDetailsSheet({Key? key, required this.job}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          top: false,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Drag handle
                  Container(
                    width: MediaQuery.of(context).size.width * 0.1, // Responsive width
                    height: MediaQuery.of(context).size.height * 0.005, // Responsive height
                    margin: EdgeInsets.symmetric(vertical: MediaQuery.of(context).size.height * 0.015), // Responsive margin
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * 0.06), // Responsive padding
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          job.jobTitle,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: MediaQuery.of(context).size.width * 0.055), // Responsive font size
                        ),
                        if (job.company.isNotEmpty) ...[
                          SizedBox(height: MediaQuery.of(context).size.height * 0.01), // Responsive height
                          Text(
                            job.company,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontSize: MediaQuery.of(context).size.width * 0.045), // Responsive font size
                          ),
                        ],
                        Divider(height: MediaQuery.of(context).size.height * 0.04), // Responsive height
                        _buildRow(context, 'Classification', job.classification),
                        _buildRow(context, 'Location', job.location),
                        _buildRow(context, 'Wage', job.wage),
                        _buildRow(context, 'Hours', job.hours),
                        _buildRow(context, 'Per Diem', job.perDiem),
                        _buildRow(context, 'Start Date', job.startDate),
                        _buildRow(context, 'Start Time', job.startTime),
                        _buildRow(context, 'Date Posted', job.datePosted),
                        _buildRow(context, 'Qualifications', job.qualifications),
                        _buildRow(context, 'Job Description', job.jobDescription),
                        _buildRow(context, 'Agreement', job.agreement),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.03), // Responsive height
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              // TODO: implement bid for job callback
                            },
                            style: ElevatedButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: MediaQuery.of(context).size.height * 0.018), // Responsive padding
                            ),
                            child: Text(
                              'Bid for Job',
                              style: TextStyle(fontSize: MediaQuery.of(context).size.width * 0.04), // Responsive font size
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRow(BuildContext context, String label, String value) {
    if (value.isEmpty) return SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.symmetric(vertical: MediaQuery.of(context).size.height * 0.005), // Responsive padding
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start, // Align text to start for multi-line values
        children: [
          Text(
            '$label: ',
            style: Theme.of(context)
                .textTheme
                .titleSmall
                ?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: MediaQuery.of(context).size.width * 0.038, // Responsive font size
                ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: MediaQuery.of(context).size.width * 0.038, // Responsive font size
                  ),
            ),
          ),
        ],
      ),
    );
  }
}