import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'stepper_model.dart';
export 'stepper_model.dart';

class StepperWidget extends StatefulWidget {
  const StepperWidget({super.key});

  @override
  State<StepperWidget> createState() => _StepperWidgetState();
}

class _StepperWidgetState extends State<StepperWidget> {
  late StepperModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => StepperModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Align(
          alignment: AlignmentDirectional(0.0, 0.0),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.12, // Responsive width
            height: MediaQuery.of(context).size.width * 0.12, // Responsive height
            decoration: BoxDecoration(
              color: FFAppState().onBoardingStatus == 0
                  ? FlutterFlowTheme.of(context).accent1
                  : Color(0x37333333),
              borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width * 0.06), // Responsive radius
            ),
            child: Align(
              alignment: AlignmentDirectional(0.0, 0.0),
              child: Text(
                '1',
                style: FlutterFlowTheme.of(context).labelLarge.override(
                      font: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontStyle:
                            FlutterFlowTheme.of(context).labelLarge.fontStyle,
                      ),
                      fontSize: MediaQuery.of(context).size.width * 0.055, // Responsive font size
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                      fontStyle:
                          FlutterFlowTheme.of(context).labelLarge.fontStyle,
                    ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            // width: 100.0, // Expanded takes care of width
            height: FFAppState().onBoardingStatus > 0 ? MediaQuery.of(context).size.height * 0.0025 : MediaQuery.of(context).size.height * 0.0012, // Responsive height
            decoration: BoxDecoration(
              color: FFAppState().onBoardingStatus > 0
                  ? FlutterFlowTheme.of(context).warning
                  : Color(0x37333333),
            ),
          ),
        ),
        Align(
          alignment: AlignmentDirectional(0.0, 0.0),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.12, // Responsive width
            height: MediaQuery.of(context).size.width * 0.12, // Responsive height
            decoration: BoxDecoration(
              color: FFAppState().onBoardingStatus == 1
                  ? FlutterFlowTheme.of(context).accent1
                  : Color(0x37333333),
              borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width * 0.06), // Responsive radius
            ),
            child: Align(
              alignment: AlignmentDirectional(0.0, 0.0),
              child: Text(
                '2',
                style: FlutterFlowTheme.of(context).labelLarge.override(
                      font: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontStyle:
                            FlutterFlowTheme.of(context).labelLarge.fontStyle,
                      ),
                      fontSize: MediaQuery.of(context).size.width * 0.055, // Responsive font size
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                      fontStyle:
                          FlutterFlowTheme.of(context).labelLarge.fontStyle,
                    ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            // width: 100.0, // Expanded takes care of width
            height: FFAppState().onBoardingStatus > 0 ? MediaQuery.of(context).size.height * 0.0025 : MediaQuery.of(context).size.height * 0.0012, // Responsive height
            decoration: BoxDecoration(
              color: FFAppState().onBoardingStatus == 2
                  ? FlutterFlowTheme.of(context).warning
                  : Color(0x37333333),
            ),
          ),
        ),
        Align(
          alignment: AlignmentDirectional(0.0, 0.0),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.12, // Responsive width
            height: MediaQuery.of(context).size.width * 0.12, // Responsive height
            decoration: BoxDecoration(
              color: FFAppState().onBoardingStatus == 2
                  ? FlutterFlowTheme.of(context).accent1
                  : Color(0x37333333),
              borderRadius: BorderRadius.circular(MediaQuery.of(context).size.width * 0.06), // Responsive radius
            ),
            child: Align(
              alignment: AlignmentDirectional(0.0, 0.0),
              child: Text(
                '3',
                style: FlutterFlowTheme.of(context).labelLarge.override(
                      font: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontStyle:
                            FlutterFlowTheme.of(context).labelLarge.fontStyle,
                      ),
                      fontSize: MediaQuery.of(context).size.width * 0.055, // Responsive font size
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                      fontStyle:
                          FlutterFlowTheme.of(context).labelLarge.fontStyle,
                    ),
              ),
            ),
          ),
        ),
      ].divide(SizedBox(width: MediaQuery.of(context).size.width * 0.015)), // Responsive width
    );
  }
}
