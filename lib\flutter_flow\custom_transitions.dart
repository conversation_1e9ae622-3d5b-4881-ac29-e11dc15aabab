// Custom page transitions for Journeyman Jobs
// See: animation_standards.md for documentation

import 'package:flutter/material.dart';
import '../utils/animation_utils.dart';

/// CustomPageRoute provides right-to-left (forward) and left-to-right (backward) transitions.
/// Use [isForward] to control direction.
class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final bool isForward;

  CustomPageRoute({
    required this.page,
    this.isForward = true,
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          settings: settings,
          transitionDuration: AnimationDurations.medium,
          reverseTransitionDuration: AnimationDurations.medium,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            final beginOffset = isForward ? const Offset(1.0, 0.0) : const Offset(-1.0, 0.0);
            final endOffset = Offset.zero;
            final tween = Tween<Offset>(begin: beginOffset, end: endOffset)
                .chain(CurveTween(curve: AnimationCurves.standard));
            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

/// BottomNavTransition provides a fade+scale effect for bottom navigation changes.
/// Use this when switching between bottom nav tabs.
class BottomNavTransition extends PageRouteBuilder {
  final Widget page;

  BottomNavTransition({required this.page, RouteSettings? settings})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          settings: settings,
          transitionDuration: AnimationDurations.short,
          reverseTransitionDuration: AnimationDurations.short,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            final fade = CurvedAnimation(
              parent: animation,
              curve: AnimationCurves.fastIn,
            );
            final scale = Tween<double>(begin: 0.96, end: 1.0)
                .chain(CurveTween(curve: AnimationCurves.fastIn))
                .animate(animation);
            return FadeTransition(
              opacity: fade,
              child: ScaleTransition(
                scale: scale,
                child: child,
              ),
            );
          },
        );
}