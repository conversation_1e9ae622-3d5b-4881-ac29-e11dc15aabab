import 'package:flutter/material.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../utils/animation_utils.dart';

class AnimationDemoWidget extends StatefulWidget {
  const AnimationDemoWidget({Key? key}) : super(key: key);

  @override
  _AnimationDemoWidgetState createState() => _AnimationDemoWidgetState();
}

class _AnimationDemoWidgetState extends State<AnimationDemoWidget> with TickerProviderStateMixin {
  final scaffoldKey = GlobalKey<ScaffoldState>();

  final Map<String, Duration> _durations = {
    'Short (150ms)': Duration(milliseconds: 150),
    'Medium (300ms)': Duration(milliseconds: 300),
    'Long (600ms)': Duration(milliseconds: 600),
    'Very Long (1000ms)': Duration(milliseconds: 1000),
  };

  final Map<String, Curve> _curves = {
    'easeInOut': Curves.easeInOut,
    'fastOutSlowIn': Curves.fastOutSlowIn,
    'easeIn': Curves.easeIn,
    'easeOut': Curves.easeOut,
    'decelerate': Curves.decelerate,
    'bounceOut': Curves.bounceOut,
    'elasticOut': Curves.elasticOut,
    'linear': Curves.linear,
  };

  late AnimationController _controller;
  bool _isAnimated = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: _durations.values.first, // Default duration
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _triggerAnimation(Duration duration, Curve curve) {
    _controller.duration = duration;
    if (_controller.isAnimating) {
      _controller.stop();
    }
    _controller.reset();
    _isAnimated = !_isAnimated; // Toggle state for implicit animations if used, or drive explicit
    // For explicit animations, you might do:
    // _controller.forward();
    // For this demo, we'll use AnimatedContainer which is implicit.
    setState(() {});
  }


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primary,
          automaticallyImplyLeading: false,
          title: Text(
            'Animation Demo',
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  fontFamily: 'Poppins',
                  color: Colors.white,
                  fontSize: 22.0,
                ),
          ),
          actions: [],
          centerTitle: false,
          elevation: 2.0,
        ),
        body: SafeArea(
          top: true,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Standard Durations & Curves',
                  style: FlutterFlowTheme.of(context).titleLarge,
                ),
                SizedBox(height: 10),
                Text(
                  'Tap a box to see it animate with the specified duration and curve.',
                  style: FlutterFlowTheme.of(context).bodyMedium,
                ),
                SizedBox(height: 10),
                _buildDurationsSection(),
                SizedBox(height: 10),
                _buildCurvesSection(),
                Divider(thickness: 1, color: FlutterFlowTheme.of(context).alternate),
                SizedBox(height: 20),

                Text(
                  'Button & Interactive Element Animations',
                  style: FlutterFlowTheme.of(context).titleLarge,
                ),
                // TODO: Add examples for button animations
                _buildButtonAnimationExamples(),
                Divider(thickness: 1, color: FlutterFlowTheme.of(context).alternate),
                SizedBox(height: 20),

                Text(
                  'List & Card Animations',
                  style: FlutterFlowTheme.of(context).titleLarge,
                ),
                // TODO: Add examples for list and card animations
                _buildListAnimationExamples(),
                Divider(thickness: 1, color: FlutterFlowTheme.of(context).alternate),
                SizedBox(height: 20),

                Text(
                  'Page Transition Effects',
                  style: FlutterFlowTheme.of(context).titleLarge,
                ),
                // TODO: Add examples/simulations for page transitions
                _buildPageTransitionExamples(),
                Divider(thickness: 1, color: FlutterFlowTheme.of(context).alternate),
                SizedBox(height: 20),

                Text(
                  'Loading & Feedback Animations',
                  style: FlutterFlowTheme.of(context).titleLarge,
                ),
                // TODO: Add examples for loading and feedback animations
                _buildLoadingAnimationExamples(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDurationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _durations.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(entry.key, style: FlutterFlowTheme.of(context).titleMedium),
              SizedBox(height: 8),
              Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children: _curves.entries.map((curveEntry) {
                  return _AnimatedDemoBox(
                    label: curveEntry.key,
                    duration: entry.value,
                    curve: curveEntry.value,
                    onTap: () => _triggerAnimation(entry.value, curveEntry.value),
                    isAnimated: _isAnimated, // This will be used by AnimatedContainer
                    controller: _controller, // Pass controller if needed for explicit
                  );
                }).toList(),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCurvesSection() {
    // This section is now integrated into _buildDurationsSection by showing all curves for each duration.
    // If a separate section just for curves (with a default duration) is desired, it can be built similarly.
    // For now, this can be a placeholder or removed.
    return SizedBox.shrink();
  }

  Widget _buildButtonAnimationExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tap Animation (Scale 90% -> 100%, 150ms, easeOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _TapAnimatedButton(),
        SizedBox(height: 16),
        Text(
          'Hover Animation (Scale/Elevation, 150ms, easeInOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _HoverAnimatedButton(),
      ],
    );
  }

  Widget _buildListAnimationExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Staggered List Animation (Fade + Slide Up, 200ms/item, easeOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _StaggeredListDemo(),
      ],
    );
  }

  Widget _buildPageTransitionExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Forward Navigation (Simulated Slide R-to-L, 300ms, fastOutSlowIn)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _TransitionSimulator(transitionType: PageTransitionType.slideRightToLeft),
        SizedBox(height: 16),
        Text(
          'Back Navigation (Simulated Slide L-to-R, 300ms, easeOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _TransitionSimulator(transitionType: PageTransitionType.slideLeftToRight),
        SizedBox(height: 16),
        Text(
          'Bottom Nav Change (Simulated Fade + Vertical, 250ms, easeInOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _TransitionSimulator(transitionType: PageTransitionType.fadeUp), // Using a more descriptive enum
      ],
    );
  }

  Widget _buildLoadingAnimationExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loading Indicator (Continuous Rotation, 800ms/rotation, linear)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _LoadingIndicatorDemo(),
        SizedBox(height: 16),
        Text(
          'Success Feedback (Bounce + Color Pulse, 400ms total, bounceOut)',
          style: FlutterFlowTheme.of(context).titleSmall,
        ),
        SizedBox(height: 8),
        _SuccessFeedbackDemo(),
      ],
    );
  }
}

class _TapAnimatedButton extends StatefulWidget {
  @override
  __TapAnimatedButtonState createState() => __TapAnimatedButtonState();
}

class __TapAnimatedButtonState extends State<_TapAnimatedButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // Will be reversed for the "up" part
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown() {
    _animationController.forward();
  }

  void _handleTapUp() {
    // Ensure it reverses from the current position, especially if tap is very short
    _animationController.reverse();
  }
  
  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _handleTapDown(),
      onTapUp: (_) => _handleTapUp(),
      onTapCancel: () => _handleTapCancel(),
      onTap: () { /* Actual button action if any */ },
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).primary,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                spreadRadius: 1,
                blurRadius: 3,
                offset: Offset(0, 1),
              )
            ],
          ),
          child: Text(
            'Tap Me',
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'Poppins',
                  color: Colors.white,
                ),
          ),
        ),
      ),
    );
  }
}

class _HoverAnimatedButton extends StatefulWidget {
  @override
  __HoverAnimatedButtonState createState() => __HoverAnimatedButtonState();
}

class __HoverAnimatedButtonState extends State<_HoverAnimatedButton> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 150),
        curve: Curves.easeInOut,
        transform: _isHovering ? (Matrix4.identity()..scale(1.05)) : Matrix4.identity(),
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondary,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(_isHovering ? 0.25 : 0.15),
              spreadRadius: _isHovering ? 2 : 1,
              blurRadius: _isHovering ? 5 : 3,
              offset: Offset(0, _isHovering ? 2 : 1),
            )
          ],
        ),
        child: Text(
          'Hover Over Me',
          style: FlutterFlowTheme.of(context).bodyMedium.override(
                fontFamily: 'Poppins',
                color: Colors.white,
              ),
        ),
      ),
    );
  }
}

class _StaggeredListDemo extends StatefulWidget {
  @override
  __StaggeredListDemoState createState() => __StaggeredListDemoState();
}

class __StaggeredListDemoState extends State<_StaggeredListDemo> with SingleTickerProviderStateMixin {
  late AnimationController _listAnimationController;
  List<String> _items = [];
  final int _itemCount = 5;

  @override
  void initState() {
    super.initState();
    _listAnimationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: _itemCount * 200), // Total duration for all items
    );
    _loadItems();
  }

  void _loadItems() {
    // Simulate loading items and then animate them in
    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _items = List.generate(_itemCount, (index) => 'Item ${index + 1}');
        });
        _listAnimationController.forward(from: 0.0);
      }
    });
  }

  @override
  void dispose() {
    _listAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () {
            if (mounted) {
              setState(() {
                _items = []; // Clear items
              });
              _listAnimationController.reset();
              _loadItems(); // Reload and re-animate
            }
          },
          child: Text('Replay List Animation'),
          style: ElevatedButton.styleFrom(
            backgroundColor: FlutterFlowTheme.of(context).primary,
            foregroundColor: Colors.white,
          ),
        ),
        SizedBox(height: 10),
        if (_items.isEmpty)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text("Loading items...", style: FlutterFlowTheme.of(context).bodySmall),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(), // Disable scrolling for this demo list
            itemCount: _items.length,
            itemBuilder: (context, index) {
              // Calculate the interval for each item's animation
              final double itemStartTime = (index * 200.0) / _listAnimationController.duration!.inMilliseconds;
              final double itemEndTime = ((index + 1) * 200.0) / _listAnimationController.duration!.inMilliseconds;

              // Ensure itemEndTime does not exceed 1.0
              final double clampedItemEndTime = itemEndTime > 1.0 ? 1.0 : itemEndTime;
              
              // Ensure itemStartTime is less than clampedItemEndTime
              final double clampedItemStartTime = itemStartTime >= clampedItemEndTime ? (clampedItemEndTime - 0.01).clamp(0.0, 1.0) : itemStartTime;


              final Animation<double> fadeAnimation = Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(
                CurvedAnimation(
                  parent: _listAnimationController,
                  curve: Interval(clampedItemStartTime, clampedItemEndTime, curve: Curves.easeOut),
                ),
              );

              final Animation<Offset> slideAnimation = Tween<Offset>(
                begin: Offset(0, 0.5), // Start off-screen below
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _listAnimationController,
                  curve: Interval(clampedItemStartTime, clampedItemEndTime, curve: Curves.easeOut),
                ),
              );

              return AnimatedBuilder(
                animation: _listAnimationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: fadeAnimation,
                    child: SlideTransition(
                      position: slideAnimation,
                      child: child,
                    ),
                  );
                },
                child: Card(
                  elevation: 2,
                  margin: EdgeInsets.symmetric(vertical: 4, horizontal: 0),
                  child: ListTile(
                    title: Text(_items[index], style: FlutterFlowTheme.of(context).bodyLarge),
                    tileColor: FlutterFlowTheme.of(context).secondaryBackground,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}

class _AnimatedDemoBox extends StatelessWidget {
  final String label;
  final Duration duration;
  final Curve curve;
  final VoidCallback onTap;
  final bool isAnimated; // Used by AnimatedContainer
  final AnimationController controller; // For potential explicit animations

  const _AnimatedDemoBox({
    Key? key,
    required this.label,
    required this.duration,
    required this.curve,
    required this.onTap,
    required this.isAnimated,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Using AnimatedContainer for simplicity to demonstrate duration and curve
    // More complex animations would use the controller with an AnimatedBuilder
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: duration,
        curve: curve,
        width: isAnimated ? 80 : 60,
        height: isAnimated ? 40 : 60,
        decoration: BoxDecoration(
          color: isAnimated ? FlutterFlowTheme.of(context).secondary : FlutterFlowTheme.of(context).primary,
          borderRadius: BorderRadius.circular(isAnimated ? 20 : 8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: isAnimated ? 2 : 0,
              blurRadius: isAnimated ? 4 : 2,
              offset: Offset(0, isAnimated ? 2 : 1),
            )
          ],
        ),
        alignment: Alignment.center,
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: FlutterFlowTheme.of(context).bodySmall.override(
                fontFamily: 'Poppins',
                color: Colors.white,
                fontSize: 8,
              ),
        ),
      ),
    );
  }
}

class _LoadingIndicatorDemo extends StatefulWidget {
  @override
  __LoadingIndicatorDemoState createState() => __LoadingIndicatorDemoState();
}

class __LoadingIndicatorDemoState extends State<_LoadingIndicatorDemo> with SingleTickerProviderStateMixin {
  late AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800),
    )..repeat(); // Continuous rotation
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _rotationController,
      child: Icon(
        Icons.refresh,
        size: 40,
        color: FlutterFlowTheme.of(context).primary,
      ),
    );
  }
}

class _SuccessFeedbackDemo extends StatefulWidget {
  @override
  __SuccessFeedbackDemoState createState() => __SuccessFeedbackDemoState();
}

class __SuccessFeedbackDemoState extends State<_SuccessFeedbackDemo> with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _colorController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  bool _showSuccess = false;

  @override
  void initState() {
    super.initState();
    _bounceController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300), // Part of the 400ms total
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.bounceOut),
    );

    _colorController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 400), // Full duration for color pulse
    );
    _colorAnimation = ColorTween(
      begin: FlutterFlowTheme.of(context).primary,
      end: FlutterFlowTheme.of(context).success,
    ).animate(
      CurvedAnimation(parent: _colorController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  void _triggerSuccess() {
    setState(() {
      _showSuccess = true;
    });
    _bounceController.forward().then((_) {
      _bounceController.reverse();
    });
    _colorController.forward().then((_) {
      Future.delayed(Duration(milliseconds: 600), () { // Hold color a bit
        if(mounted) {
          _colorController.reverse().then((_){
            if(mounted) {
              setState(() {
                _showSuccess = false;
              });
            }
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _triggerSuccess,
      child: AnimatedBuilder(
        animation: Listenable.merge([_bounceController, _colorController]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _showSuccess ? _colorAnimation.value : FlutterFlowTheme.of(context).primaryBackground,
                shape: BoxShape.circle,
                border: Border.all(
                  color: _showSuccess ? _colorAnimation.value! : FlutterFlowTheme.of(context).primary,
                  width: 2
                ),
                boxShadow: [
                  if (_showSuccess)
                    BoxShadow(
                      color: _colorAnimation.value!.withOpacity(0.5),
                      blurRadius: 8,
                      spreadRadius: 2,
                    )
                ],
              ),
              child: Icon(
                _showSuccess ? Icons.check_circle_outline : Icons.touch_app,
                size: 30,
                color: _showSuccess ? Colors.white : FlutterFlowTheme.of(context).primary,
              ),
            ),
          );
        },
      ),
    );
  }
}
enum PageTransitionType {
  slideRightToLeft,
  slideLeftToRight,
  fadeUp, // For bottom nav like effect
  // Add modal scale up if needed from standards
}

class _TransitionSimulator extends StatefulWidget {
  final PageTransitionType transitionType;

  const _TransitionSimulator({Key? key, required this.transitionType}) : super(key: key);

  @override
  __TransitionSimulatorState createState() => __TransitionSimulatorState();
}

class __TransitionSimulatorState extends State<_TransitionSimulator> with SingleTickerProviderStateMixin {
  bool _isTransitioned = false;
  late Duration _duration;
  late Curve _curve;

  @override
  void initState() {
    super.initState();
    switch (widget.transitionType) {
      case PageTransitionType.slideRightToLeft:
        _duration = AnimationDurations.medium; // 300ms
        _curve = Curves.fastOutSlowIn; // As per standards
        break;
      case PageTransitionType.slideLeftToRight:
        _duration = AnimationDurations.medium; // 300ms
        _curve = Curves.easeOut; // As per standards
        break;
      case PageTransitionType.fadeUp:
        _duration = Duration(milliseconds: 250); // As per standards
        _curve = Curves.easeInOut; // As per standards
        break;
    }
  }

  void _triggerTransition() {
    setState(() {
      _isTransitioned = !_isTransitioned;
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width - 32; // Account for padding
    Widget animatedChild;

    // Placeholder for the actual page content for simulation
    Widget _buildPageContent(String text, Color color, {bool isStatic = false}) {
      return Container(
        width: screenWidth, 
        height: 100,
        color: color,
        alignment: Alignment.center,
        child: Text(
          text,
          style: FlutterFlowTheme.of(context).titleMedium.override(
                fontFamily: 'Poppins',
                color: FlutterFlowTheme.of(context).primaryText,
              ),
        ),
      );
    }

    switch (widget.transitionType) {
      case PageTransitionType.slideRightToLeft:
        animatedChild = AnimatedPositioned(
          duration: _duration,
          curve: _curve,
          left: _isTransitioned ? -screenWidth * 0.7 : 0, 
          right: _isTransitioned ? screenWidth * 0.7 : screenWidth, // Start off-screen right
          child: _buildPageContent('Page B (New)', FlutterFlowTheme.of(context).tertiary),
        );
        break;
      case PageTransitionType.slideLeftToRight:
        animatedChild = AnimatedPositioned(
          duration: _duration,
          curve: _curve,
          left: _isTransitioned ? screenWidth * 0.7 : screenWidth, // Start off-screen left
          right: _isTransitioned ? -screenWidth * 0.7 : 0, 
          child: _buildPageContent('Page A (New)', FlutterFlowTheme.of(context).accent2),
        );
        break;
      case PageTransitionType.fadeUp:
        animatedChild = AnimatedOpacity(
          duration: _duration,
          curve: _curve,
          opacity: _isTransitioned ? 1.0 : 0.0,
          child: AnimatedContainer(
            duration: _duration,
            curve: _curve,
            transform: Matrix4.translationValues(0, _isTransitioned ? 0 : 20, 0), // Start from bottom and fade up
            child: _buildPageContent('New Tab Content', FlutterFlowTheme.of(context).accent3),
          ),
        );
        break;
    }

    return GestureDetector(
      onTap: _triggerTransition,
      child: Container(
        width: screenWidth,
        height: 100,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          border: Border.all(color: FlutterFlowTheme.of(context).alternate),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            // Static background page for slide transitions to give context
            if (widget.transitionType == PageTransitionType.slideRightToLeft)
                 _buildPageContent('Page A (Current)', FlutterFlowTheme.of(context).secondaryBackground, isStatic: true),
            if (widget.transitionType == PageTransitionType.slideLeftToRight)
                 _buildPageContent('Page B (Current)', FlutterFlowTheme.of(context).secondaryBackground, isStatic: true),
            if (widget.transitionType == PageTransitionType.fadeUp && !_isTransitioned) // Show current for fadeUp before transition
                 _buildPageContent('Old Tab Content', FlutterFlowTheme.of(context).secondaryBackground, isStatic: true),
            animatedChild,
          ],
        ),
      ),
    );
  }
}