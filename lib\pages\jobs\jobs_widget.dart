import '/auth/firebase_auth/auth_util.dart'; // Added for currentUser
import '/backend/backend.dart';
import '/utils/job_filter_utils.dart'; // Added for JobFilterUtils and PaginatedJobsResult
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/utils/animation_utils.dart';
import '../../widgets/common/loading_indicator_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'jobs_model.dart';
export 'jobs_model.dart';

class JobsWidget extends StatefulWidget {
  const JobsWidget({super.key});

  static String routeName = 'jobs';
  static String routePath = '/jobs';

  @override
  State<JobsWidget> createState() => _JobsWidgetState();
}

class _JobsWidgetState extends State<JobsWidget> {
  late JobsModel _model;
  String? _selectedJobId;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  List<JobsRecord> _jobs = [];
  DocumentSnapshot? _lastDocument;
  bool _isLoading = false; // For initial load and refresh
  bool _isLoadingMore = false; // For loading more items
  bool _hasMore = true;
  final ScrollController _scrollController = ScrollController();
  final JobFilterUtils _jobFilterUtils = JobFilterUtils();
  UsersRecord? _currentUser;
  Map<String, dynamic> _currentFilters = {}; // To store active filters
  static const int _pageSize = 15; // Number of jobs per page

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => JobsModel());
    _loadCurrentUserAndFetchInitialJobs();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadCurrentUserAndFetchInitialJobs() async {
    final baseAuthUser = currentUser;
    if (baseAuthUser != null && baseAuthUser.loggedIn) {
      try {
        _currentUser = await UsersRecord.getDocumentOnce(UsersRecord.collection.doc(baseAuthUser.uid));
        if (mounted && _currentUser != null) {
          _fetchInitialJobs();
        } else if (mounted) {
          print('Error: Could not fetch UsersRecord or component unmounted.');
          setState(() => _isLoading = false);
        }
      } catch (e) {
        print('Error fetching UsersRecord: $e');
        if (mounted) setState(() => _isLoading = false);
      }
    } else if (mounted) {
      // Handle case where user is not logged in
      setState(() {
        _isLoading = false;
      });
      print('Error: Current user not logged in or available.');
    } else if (mounted) {
      // Handle case where user is not available
      setState(() {
        _isLoading = false;
        // Optionally show an error or a login prompt
      });
      print('Error: Current user not available.');
    }
  }

  Future<void> _fetchInitialJobs({Map<String, dynamic>? newFilters}) async {
    if (_isLoading) return;
    if (_currentUser == null) {
        print("Cannot fetch jobs, user not loaded.");
        return;
    }

    setState(() {
      _isLoading = true;
      _hasMore = true; // Assume there's more data when fetching initially or with new filters
      _lastDocument = null;
      _jobs.clear();
      if (newFilters != null) {
        _currentFilters = newFilters;
      }
    });

    try {
      final result = await _jobFilterUtils.fetchAndProcessJobs(
        user: _currentUser!,
        currentFilters: _currentFilters,
        pageLimit: _pageSize,
        lastFetchedDoc: null, // Always null for initial fetch
        forceRefresh: true, // Ensure fresh data on initial load/filter change
      );
      if (mounted) {
        setState(() {
          _jobs = result.jobs;
          _lastDocument = result.lastDocument;
          _hasMore = result.hasMore;
        });
      }
    } catch (e) {
      print('Error fetching initial jobs: $e');
      if (mounted) {
        // Optionally show an error message to the user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading jobs. Please try again.')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchMoreJobs() async {
    if (_isLoading || _isLoadingMore || !_hasMore || _currentUser == null) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final result = await _jobFilterUtils.fetchAndProcessJobs(
        user: _currentUser!,
        currentFilters: _currentFilters,
        pageLimit: _pageSize,
        lastFetchedDoc: _lastDocument,
      );
      if (mounted) {
        setState(() {
          _jobs.addAll(result.jobs);
          _lastDocument = result.lastDocument;
          _hasMore = result.hasMore;
        });
      }
    } catch (e) {
      print('Error fetching more jobs: $e');
       if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading more jobs.')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 && // Trigger a bit before the end
        !_isLoading &&
        !_isLoadingMore &&
        _hasMore) {
      _fetchMoreJobs();
    }
  }
  
  Future<void> _onRefresh() async {
     await _fetchInitialJobs();
  }

  @override
  void dispose() {
    _model.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // Placeholder for filter update logic
  void _applyFiltersAndReload(Map<String, dynamic> newFilters) {
    _fetchInitialJobs(newFilters: newFilters);
  }


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: NestedScrollView(
          floatHeaderSlivers: true,
          headerSliverBuilder: (context, _) => [
            SliverAppBar(
              pinned: true,
              floating: false,
              backgroundColor: FlutterFlowTheme.of(context).primary,
              automaticallyImplyLeading: false,
              title: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Jobs', // Removed extra spaces
                    style: FlutterFlowTheme.of(context).headlineMedium.override(
                          font: GoogleFonts.poppins(
                            fontWeight: FlutterFlowTheme.of(context)
                                .headlineMedium
                                .fontWeight,
                            fontStyle: FlutterFlowTheme.of(context)
                                .headlineMedium
                                .fontStyle,
                          ),
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.width * 0.055, // Responsive font size
                          letterSpacing: 0.0,
                          fontWeight: FlutterFlowTheme.of(context)
                              .headlineMedium
                              .fontWeight,
                          fontStyle: FlutterFlowTheme.of(context)
                              .headlineMedium
                              .fontStyle,
                        ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      ButtonPressEffect(
                        onTap: () {
                          print('Filter button pressed - Placeholder');
                          // Example: Show a filter dialog, then on apply:
                          // _applyFiltersAndReload({'jobType': 'Welder'});
                        },
                        child: Icon(
                          Icons.filter_alt,
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                        ),
                      ),
                      ButtonPressEffect(
                        onTap: () {
                          print('Search button pressed - Placeholder');
                          // Example: Show a search dialog, then on apply:
                          // _applyFiltersAndReload({'searchTerm': 'keyword'});
                        },
                        child: Icon(
                          Icons.search_sharp,
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                        ),
                      ),
                    ].divide(SizedBox(width: MediaQuery.of(context).size.width * 0.04)), // Responsive width
                  ),
                ],
              ),
              actions: [],
              centerTitle: false,
              elevation: 2.0,
            )
          ],
          body: Builder(
            builder: (context) {
              return SafeArea(
                top: false,
                child: Padding(
                  padding: EdgeInsets.all(15.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              FFButtonWidget(
                                onPressed: () {
                                  print('AllJobsButton pressed ...');
                                  _applyFiltersAndReload({}); // Clear filters
                                },
                                text: 'All Jobs',
                                options: FFButtonOptions(
                                  height: MediaQuery.of(context).size.height * 0.05, // Responsive height
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      MediaQuery.of(context).size.width * 0.04, 0.0, MediaQuery.of(context).size.width * 0.04, 0.0), // Responsive padding
                                  iconPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 0.0),
                                  color: FlutterFlowTheme.of(context).accent1,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        font: GoogleFonts.poppins(
                                          fontWeight: FontWeight.normal,
                                          fontStyle:
                                              FlutterFlowTheme.of(context)
                                                  .titleSmall
                                                  .fontStyle,
                                        ),
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        fontSize: MediaQuery.of(context).size.width * 0.035, // Responsive font size
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        fontStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .fontStyle,
                                      ),
                                  elevation: 0.0,
                                  borderRadius: BorderRadius.circular(15.0),
                                ),
                              ),
                              Builder(
                                builder: (context) {
                                  final jobsCat = // Renamed to avoid conflict with _jobs
                                      FFAppConstants.construction.toList();

                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: List.generate(jobsCat.length, // Use renamed variable
                                          (idx) { // Use different index name to avoid conflict
                                        final jobCategory = jobsCat[idx]; // Use renamed variable
                                        return FFButtonWidget(
                                          onPressed: () {
                                            print('Button pressed for ${jobCategory}');
                                            _applyFiltersAndReload({'jobType': jobCategory});
                                          },
                                          text: jobCategory, // Use dynamic text
                                          options: FFButtonOptions(
                                            height: MediaQuery.of(context).size.height * 0.05, // Responsive height
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    MediaQuery.of(context).size.width * 0.04, 0.0, MediaQuery.of(context).size.width * 0.04, 0.0), // Responsive padding
                                            iconPadding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 0.0),
                                            color: Color(0x19666666),
                                            textStyle:
                                                FlutterFlowTheme.of(context)
                                                    .labelSmall
                                                    .override(
                                                      font: GoogleFonts.poppins(
                                                        fontWeight:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .labelSmall
                                                                .fontWeight,
                                                        fontStyle:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .labelSmall
                                                                .fontStyle,
                                                      ),
                                                      fontSize: MediaQuery.of(context).size.width * 0.035, // Responsive font size
                                                      letterSpacing: 0.0,
                                                      fontWeight:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .labelSmall
                                                              .fontWeight,
                                                      fontStyle:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .labelSmall
                                                              .fontStyle,
                                                    ),
                                            elevation: 0.0,
                                            borderRadius:
                                                BorderRadius.circular(15.0),
                                          ),
                                        );
                                      }).divide(SizedBox(width: MediaQuery.of(context).size.width * 0.03)), // Responsive width
                                    ),
                                  );
                                },
                              ),
                            ].divide(SizedBox(width: MediaQuery.of(context).size.width * 0.03)), // Responsive width,
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _currentFilters.containsKey('jobType') && _currentFilters['jobType'] != null
                                ? '${_currentFilters['jobType']} Jobs'
                                : 'All Jobs',
                              style: FlutterFlowTheme.of(context)
                                  .titleMedium
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w600,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .titleMedium
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.045, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .titleMedium
                                        .fontStyle,
                                  ),
                            ),
                            _buildJobList(), // Replaced StreamBuilder with a method
                          ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.02)), // Responsive height
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildJobList() {
    if (_isLoading && _jobs.isEmpty) {
      return const Center(child: LoadingIndicatorWidget());
    }

    if (!_isLoading && _jobs.isEmpty && !_hasMore) {
      return const EmptyStateWidget(
        title: 'No Jobs Available',
        message: 'There are currently no job listings. Check back later for new opportunities.',
        icon: Icons.work_off_outlined,
      );
    }
    
    // If jobs list is empty but we are not loading and potentially have more,
    // it might be an initial state before first fetch completes or an error.
    // The _isLoading flag should cover the loading state.
    // If _jobs is empty and _isLoading is false, but _hasMore is true,
    // it implies an issue or that the first fetch hasn't populated yet.
    // However, _fetchInitialJobs sets _isLoading to true.
    // This case should ideally be covered by the EmptyStateWidget if _hasMore becomes false after a fetch.

    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: FlutterFlowTheme.of(context).primary,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      child: ListView.separated(
        controller: _scrollController,
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(), // ListView scrolling handled by NestedScrollView or outer SingleChildScrollView
        scrollDirection: Axis.vertical,
        itemCount: _jobs.length + (_isLoadingMore ? 1 : 0), // +1 for loading indicator if loading more
        separatorBuilder: (_, __) =>
            SizedBox(height: MediaQuery.of(context).size.height * 0.015),
        itemBuilder: (context, index) {
          if (index == _jobs.length && _isLoadingMore) { // Check _isLoadingMore
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: const Center(child: LoadingIndicatorWidget()),
            );
          }
          // This check should prevent errors if itemCount is temporarily out of sync
          if (index >= _jobs.length) {
            return SizedBox.shrink();
          }
          final listViewJobsRecord = _jobs[index];
                                      return StaggeredListAnimation(
                                        index: index, // Corrected from listViewIndex
                                        child: HoverScaleEffect(
                                          isSelected: _selectedJobId == listViewJobsRecord.reference.id,
                                          selectedBorderColor: FlutterFlowTheme.of(context).primary,
                                          onTap: () {
                                            setState(() {
                                              _selectedJobId = listViewJobsRecord.reference.id;
                                            });
                                            print('Job card tapped: ${listViewJobsRecord.jobTitle}');
                                            // Potentially navigate or show details
                                            // TODO: Implement JobDetailsSheet display or navigation
                                            // showModalBottomSheet(
                                            //   isScrollControlled: true,
                                            //   backgroundColor: Colors.transparent,
                                            //   context: context,
                                            //   builder: (context) {
                                            //     return Padding(
                                            //       padding: MediaQuery.of(context).viewInsets,
                                            //       child: JobDetailsSheet(job: listViewJobsRecord), // Make sure JobDetailsSheet is imported and available
                                            //     );
                                            //   },
                                            // );
                                          },
                                          child: RippleEffect( // Keep RippleEffect for tap feedback if desired
                                            borderRadius: BorderRadius.circular(12.0),
                                            // onTap is now handled by HoverScaleEffect,
                                            // but can be kept if specific ripple behavior is needed independent of hover/selection
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Color(0x19666666),
                                                borderRadius:
                                                    BorderRadius.circular(12.0),
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04), // Responsive padding
                                                child: Column(
                                            mainAxisSize: MainAxisSize.min, // Changed to min
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Expanded( // Wrap RichText with Expanded
                                                    child: RichText(
                                                      textScaler:
                                                          MediaQuery.of(context)
                                                              .textScaler,
                                                      text: TextSpan(
                                                        children: [
                                                          TextSpan(
                                                            text: 'Local: ', // Added space
                                                            style:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .bodySmall
                                                                    .override(
                                                                      font: GoogleFonts
                                                                          .poppins(
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontStyle,
                                                                      ),
                                                                      color: FlutterFlowTheme.of(
                                                                              context)
                                                                          .secondaryText,
                                                                      fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontWeight,
                                                                      fontStyle: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontStyle,
                                                                    ),
                                                          ),
                                                          TextSpan(
                                                            text: listViewJobsRecord
                                                                .localNumber
                                                                .toString(),
                                                            style:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .bodySmall
                                                                    .override(
                                                                      font: GoogleFonts
                                                                          .poppins(
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontStyle,
                                                                      ),
                                                                      fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontWeight,
                                                                      fontStyle: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontStyle,
                                                                    ),
                                                          )
                                                        ],
                                                        style: FlutterFlowTheme.of(
                                                                context)
                                                            .bodySmall
                                                            .override(
                                                              font: GoogleFonts
                                                                  .poppins(
                                                                fontWeight:
                                                                    FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodySmall
                                                                        .fontWeight,
                                                                fontStyle:
                                                                    FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodySmall
                                                                        .fontStyle,
                                                              ),
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .secondaryText,
                                                              fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                              letterSpacing: 0.0,
                                                              fontWeight:
                                                                  FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodySmall
                                                                      .fontWeight,
                                                              fontStyle:
                                                                  FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodySmall
                                                                      .fontStyle,
                                                            ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox( // This SizedBox acts as a flexible spacer
                                                    width: MediaQuery.of(context).size.width * 0.02,
                                                    child: VerticalDivider(
                                                      thickness: 2.0,
                                                      color: FlutterFlowTheme.of(
                                                              context)
                                                          .secondaryText,
                                                    ),
                                                  ),
                                                  Expanded( // Wrap Classification Text with Expanded
                                                    child: Text(
                                                      listViewJobsRecord
                                                          .classification,
                                                      textAlign: TextAlign.end, // Align text to the end
                                                      style: FlutterFlowTheme.of(
                                                              context)
                                                          .labelLarge
                                                          .override(
                                                            font:
                                                                GoogleFonts.poppins(
                                                              fontWeight:
                                                                  FontWeight.w600,
                                                              fontStyle: FlutterFlowTheme.of(context).labelLarge.fontStyle, // Added fontStyle
                                                            ),
                                                            fontSize: MediaQuery.of(context).size.width * 0.035, // Responsive font size
                                                            letterSpacing: 0.0,
                                                            fontWeight: FontWeight.w600,
                                                             fontStyle: FlutterFlowTheme.of(context).labelLarge.fontStyle, // Added fontStyle
                                                          ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Expanded(
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min, // Changed to min
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment.center,
                                                    children: [
                                                      Padding( // Added padding for icon
                                                        padding: EdgeInsetsDirectional.fromSTEB(0,0,MediaQuery.of(context).size.width * 0.01,0),
                                                        child: Icon(
                                                          Icons.access_time,
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          size: MediaQuery.of(context).size.width * 0.045, // Responsive icon size
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          listViewJobsRecord.datePosted.isNotEmpty
                                                              ? 'Posted ${listViewJobsRecord.datePosted}'
                                                              : 'Recently posted',
                                                          style:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .bodySmall
                                                                  .override(
                                                                    font: GoogleFonts
                                                                        .poppins(
                                                                      fontWeight: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontWeight,
                                                                      fontStyle: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontStyle,
                                                                    ),
                                                                    fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodySmall
                                                                        .fontWeight,
                                                                    fontStyle: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodySmall
                                                                        .fontStyle,
                                                                  ),
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min, // Changed to min
                                                    mainAxisAlignment: MainAxisAlignment.end, // Align to end
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment.center,
                                                    children: [
                                                      Padding( // Added padding for icon
                                                        padding: EdgeInsetsDirectional.fromSTEB(0,0,MediaQuery.of(context).size.width * 0.01,0),
                                                        child: Icon(
                                                          FFIcons.klocation, // Assuming FFIcons.klocation is defined
                                                          color:
                                                              Color(0xD1000000),
                                                          size: MediaQuery.of(context).size.width * 0.045, // Responsive icon size
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: RichText(
                                                          textAlign: TextAlign.end, // Align text to end
                                                          textScaler:
                                                              MediaQuery.of(context)
                                                                  .textScaler,
                                                          overflow: TextOverflow.ellipsis,
                                                          text: TextSpan(
                                                            children: [
                                                              TextSpan(
                                                                text:
                                                                    listViewJobsRecord
                                                                        .location,
                                                                style: FlutterFlowTheme
                                                                        .of(context)
                                                                    .bodySmall
                                                                    .override(
                                                                      font: GoogleFonts
                                                                          .poppins(
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontStyle,
                                                                      ),
                                                                      fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontWeight,
                                                                      fontStyle: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontStyle,
                                                                    ),
                                                              )
                                                            ],
                                                            style:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .bodySmall
                                                                    .override(
                                                                      font: GoogleFonts
                                                                          .poppins(
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodySmall
                                                                            .fontStyle,
                                                                      ),
                                                                      color: FlutterFlowTheme.of(
                                                                              context)
                                                                          .secondaryText,
                                                                      fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontWeight,
                                                                      fontStyle: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodySmall
                                                                          .fontStyle,
                                                                    ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Padding( // Added Padding for consistency
                                              padding: EdgeInsetsDirectional.fromSTEB(0.0, MediaQuery.of(context).size.height * 0.005, 0.0, MediaQuery.of(context).size.height * 0.005),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Expanded(
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment.center,
                                                      children: [
                                                        Padding(
                                                          padding: EdgeInsetsDirectional.fromSTEB(0,0,MediaQuery.of(context).size.width * 0.01,0),
                                                          child: FaIcon(
                                                            FontAwesomeIcons
                                                                .briefcase,
                                                            color:
                                                                Color(0xD1000000),
                                                            size: MediaQuery.of(context).size.width * 0.04, // Responsive icon size
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: RichText(
                                                            textScaler:
                                                                MediaQuery.of(context)
                                                                    .textScaler,
                                                            overflow: TextOverflow.ellipsis,
                                                            text: TextSpan(
                                                              children: [
                                                                TextSpan(
                                                                  text: 'Hours: ',
                                                                  style: FlutterFlowTheme
                                                                          .of(context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontWeight,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                                ),
                                                                TextSpan(
                                                                  text:
                                                                      listViewJobsRecord
                                                                          .hours,
                                                                  style: FlutterFlowTheme
                                                                          .of(context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight:
                                                                              FontWeight
                                                                                  .bold,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .bold,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                                )
                                                              ],
                                                              style:
                                                                  FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontWeight,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      mainAxisAlignment: MainAxisAlignment.end,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment.center,
                                                      children: [
                                                        Padding(
                                                          padding: EdgeInsetsDirectional.fromSTEB(0,0,MediaQuery.of(context).size.width * 0.01,0),
                                                          child: FaIcon(
                                                            FontAwesomeIcons
                                                                .dollarSign,
                                                            color:
                                                                Color(0xD1000000),
                                                            size: MediaQuery.of(context).size.width * 0.04, // Responsive icon size
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: RichText(
                                                            textAlign: TextAlign.end,
                                                            textScaler:
                                                                MediaQuery.of(context)
                                                                    .textScaler,
                                                            overflow: TextOverflow.ellipsis,
                                                            text: TextSpan(
                                                              children: [
                                                                TextSpan(
                                                                  text: listViewJobsRecord.wage.isNotEmpty ? 'Wage: ' : 'Per Diem: ', // Dynamic label
                                                                  style: FlutterFlowTheme
                                                                          .of(context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontWeight,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                                ),
                                                                TextSpan(
                                                                  text:
                                                                      listViewJobsRecord.wage.isNotEmpty
                                                                          ? listViewJobsRecord.wage
                                                                          : listViewJobsRecord.perDiem,
                                                                  style: FlutterFlowTheme
                                                                          .of(context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight:
                                                                              FontWeight
                                                                                  .bold,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .bold,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                                )
                                                              ],
                                                              style:
                                                                  FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        font: GoogleFonts
                                                                            .poppins(
                                                                          fontWeight: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontWeight,
                                                                          fontStyle: FlutterFlowTheme.of(
                                                                                  context)
                                                                              .bodyMedium
                                                                              .fontStyle,
                                                                        ),
                                                                        fontSize: MediaQuery.of(context).size.width * 0.032, // Responsive font size
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontWeight,
                                                                        fontStyle: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .fontStyle,
                                                                      ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),

                                            Padding( // Added Padding for the button
                                              padding: EdgeInsetsDirectional.fromSTEB(0.0, MediaQuery.of(context).size.height * 0.01, 0.0, 0.0),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  Expanded(
                                                    child: FFButtonWidget(
                                                      onPressed: () {
                                                        print(
                                                          'ViewDetailsButton pressed ...');
                                                    },
                                                    text: 'View Details',
                                                    options: FFButtonOptions(
                                                      height: MediaQuery.of(context).size.height * 0.05, // Responsive height
                                                      padding:
                                                          EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  MediaQuery.of(context).size.width * 0.04, // Responsive padding
                                                                  0.0,
                                                                  MediaQuery.of(context).size.width * 0.04, // Responsive padding
                                                                  0.0),
                                                      iconPadding:
                                                          EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  0.0,
                                                                  0.0,
                                                                  0.0,
                                                                  0.0),
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .accent1,
                                                      textStyle:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .titleSmall
                                                              .override(
                                                                font: GoogleFonts
                                                                    .poppins(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  fontStyle: FlutterFlowTheme.of(
                                                                          context)
                                                                      .titleSmall
                                                                      .fontStyle,
                                                                ),
                                                                color: FlutterFlowTheme.of(
                                                                        context)
                                                                    .primaryText,
                                                                fontSize: MediaQuery.of(context).size.width * 0.035, // Responsive font size
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                                fontStyle: FlutterFlowTheme.of(
                                                                        context)
                                                                    .titleSmall
                                                                    .fontStyle,
                                                              ),
                                                      elevation: 0.0,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8.0),
                                                    ),
                                                    showLoadingIndicator: false,
                                                  ),
                                                ),
                                                SizedBox(width: MediaQuery.of(context).size.width * 0.04), // Responsive SizedBox
                                                Expanded(
                                                  child: FFButtonWidget(
                                                    onPressed: () {
                                                      print(
                                                          'BidNowButton pressed ...');
                                                    },
                                                    text: 'Bid now',
                                                    options: FFButtonOptions(
                                                      height: MediaQuery.of(context).size.height * 0.05, // Responsive height
                                                      padding:
                                                          EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  MediaQuery.of(context).size.width * 0.04, // Responsive padding
                                                                  0.0,
                                                                  MediaQuery.of(context).size.width * 0.04, // Responsive padding
                                                                  0.0),
                                                      iconPadding:
                                                          EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  0.0,
                                                                  0.0,
                                                                  0.0,
                                                                  0.0),
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primary,
                                                      textStyle:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .titleSmall
                                                              .override(
                                                                font: GoogleFonts
                                                                    .poppins(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  fontStyle: FlutterFlowTheme.of(
                                                                          context)
                                                                      .titleSmall
                                                                      .fontStyle,
                                                                ),
                                                                color: Colors
                                                                    .white,
                                                                fontSize: MediaQuery.of(context).size.width * 0.035, // Responsive font size
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                                fontStyle: FlutterFlowTheme.of(
                                                                        context)
                                                                    .titleSmall
                                                                    .fontStyle,
                                                              ),
                                                      elevation: 0.0,
                                                      borderSide: BorderSide(
                                                        width: 1.5,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10.0),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
        },
      ),
    );
  }
}
