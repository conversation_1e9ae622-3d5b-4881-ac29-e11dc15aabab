import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'help_support_model.dart';
export 'help_support_model.dart';

class HelpSupportWidget extends StatefulWidget {
  const HelpSupportWidget({super.key});

  static String routeName = 'help_support';
  static String routePath = '/helpSupport';

  @override
  State<HelpSupportWidget> createState() => _HelpSupportWidgetState();
}

class _HelpSupportWidgetState extends State<HelpSupportWidget> {
  late HelpSupportModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HelpSupportModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primary,
          automaticallyImplyLeading: false,
          title: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              InkWell(
                splashColor: Colors.transparent,
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  context.safePop();
                },
                child: Icon(
                  Icons.arrow_back,
                  color: FlutterFlowTheme.of(context).primaryBackground,
                  size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                ),
              ),
              Text(
                'Help & Support',
                style: FlutterFlowTheme.of(context).headlineMedium.override(
                      font: GoogleFonts.poppins(
                        fontWeight: FlutterFlowTheme.of(context)
                            .headlineMedium
                            .fontWeight,
                        fontStyle: FlutterFlowTheme.of(context)
                            .headlineMedium
                            .fontStyle,
                      ),
                      color: Colors.white,
                      fontSize: MediaQuery.of(context).size.width * 0.055, // Responsive font size
                      letterSpacing: 0.0,
                      fontWeight: FlutterFlowTheme.of(context)
                          .headlineMedium
                          .fontWeight,
                      fontStyle:
                          FlutterFlowTheme.of(context).headlineMedium.fontStyle,
                    ),
              ),
            ].divide(SizedBox(width: MediaQuery.of(context).size.width * 0.025)), // Responsive width
          ),
          actions: [],
          centerTitle: false,
          elevation: 2.0,
        ),
        body: SafeArea(
          top: true,
          child: Padding(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04), // Responsive padding
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact Support',
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontStyle: FlutterFlowTheme.of(context)
                                  .titleMedium
                                  .fontStyle,
                            ),
                            fontSize: MediaQuery.of(context).size.width * 0.045, // Responsive font size
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            fontStyle: FlutterFlowTheme.of(context)
                                .titleMedium
                                .fontStyle,
                          ),
                    ),
                    ListView(
                      padding: EdgeInsets.zero,
                      primary: false,
                      shrinkWrap: true,
                      scrollDirection: Axis.vertical,
                      children: [
                        Material(
                          color: Colors.transparent,
                          child: ListTile(
                            leading: Icon(
                              Icons.email_outlined,
                              color: Color(0xEA000000),
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            title: Text(
                              'Email Support',
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.04, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            tileColor: Color(0x19666666),
                            dense: false,
                            contentPadding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02), // Responsive padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          child: ListTile(
                            leading: Icon(
                              Icons.call_rounded,
                              color: Color(0xEA000000),
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            title: Text(
                              'Phone Support',
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.04, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            tileColor: Color(0x19666666),
                            dense: false,
                            contentPadding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02), // Responsive padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.012)), // Responsive height
                    ),
                  ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.02)), // Responsive height
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Frequently Asked Questions',
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontStyle: FlutterFlowTheme.of(context)
                                  .titleMedium
                                  .fontStyle,
                            ),
                            fontSize: MediaQuery.of(context).size.width * 0.045, // Responsive font size
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            fontStyle: FlutterFlowTheme.of(context)
                                .titleMedium
                                .fontStyle,
                          ),
                    ),
                    ListView(
                      padding: EdgeInsets.zero,
                      primary: false,
                      shrinkWrap: true,
                      scrollDirection: Axis.vertical,
                      children: [
                        Material(
                          color: Colors.transparent,
                          child: ListTile(
                            leading: Icon(
                              Icons.question_mark,
                              color: Color(0xEA000000),
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            title: Text(
                              'How to Update Credentials',
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.04, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            tileColor: Color(0x19666666),
                            dense: false,
                            contentPadding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02), // Responsive padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          child: ListTile(
                            leading: Icon(
                              Icons.question_mark,
                              color: Color(0xEA000000),
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            title: Text(
                              'Job Application Process',
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.04, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            tileColor: Color(0x19666666),
                            dense: false,
                            contentPadding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02), // Responsive padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                        Material(
                          color: Colors.transparent,
                          child: ListTile(
                            leading: Icon(
                              Icons.question_mark,
                              color: Color(0xEA000000),
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            title: Text(
                              'Book Signing Rules',
                              style: FlutterFlowTheme.of(context)
                                  .labelLarge
                                  .override(
                                    font: GoogleFonts.poppins(
                                      fontWeight: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontWeight,
                                      fontStyle: FlutterFlowTheme.of(context)
                                          .labelLarge
                                          .fontStyle,
                                    ),
                                    fontSize: MediaQuery.of(context).size.width * 0.04, // Responsive font size
                                    letterSpacing: 0.0,
                                    fontWeight: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontWeight,
                                    fontStyle: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .fontStyle,
                                  ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: MediaQuery.of(context).size.width * 0.06, // Responsive icon size
                            ),
                            tileColor: Color(0x19666666),
                            dense: false,
                            contentPadding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02), // Responsive padding
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.012)), // Responsive height
                    ),
                  ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.02)), // Responsive height
                ),
              ].divide(SizedBox(height: MediaQuery.of(context).size.height * 0.02)), // Responsive height
            ),
          ),
        ),
      ),
    );
  }
}
