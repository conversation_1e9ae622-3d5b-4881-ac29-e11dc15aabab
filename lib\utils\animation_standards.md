# Journeyman Jobs Animation Standards

## Animation Durations

| Type   | Duration | Use Case |
|--------|----------|----------|
| Short  | 150ms    | Micro-interactions, button taps, quick state changes |
| Medium | 300ms    | Page transitions, modal appearances, standard animations |
| Long   | 600ms    | Prominent animations, complex transitions, attention-grabbing effects |
| Very Long | 1000ms   | Extended transitions, background animations, subtle continuous effects |

## Animation Curves

| Curve       | Use Case |
|-------------|----------|
| easeInOut   | Standard transitions between states |
| fastOutSlowIn | Entering elements with impact |
| easeIn      | Elements exiting view |
| easeOut     | Elements entering view |
| decelerate  | Natural stopping motions |
| bounceOut   | Playful, attention-grabbing effects |
| elasticOut  | Exaggerated, elastic effects |
| linear      | Consistent, non-accelerating motion (e.g., loading spinners) |

## Transition Guidelines

### Page Navigation

- **Forward navigation**: Right-to-left slide (300ms, fastOutSlowIn)
- **Back navigation**: Left-to-right slide (300ms, easeOut)
- **Bottom nav changes**: Fade + slight vertical movement (250ms, easeInOut)

### Component Transitions

- **Modal appearance**: Scale up + fade (300ms, fastOutSlowIn)
- **List items**: Staggered fade + slide up (200ms/item, easeOut)
- **Button taps**: Subtle scale (90% → 100%, 150ms, easeOut)

### Micro-interactions

- **Hover states**: Subtle scale or elevation change (150ms, easeInOut)
- **Loading indicators**: Continuous rotation (800ms/rotation, linear)
- **Success states**: Quick bounce + color pulse (400ms total, bounceOut)

## Implementation Notes

- Always prefer implicit animations for simple state changes
- Use explicit animations for complex sequences
- Maintain 60fps performance - avoid animating expensive properties
- Test animations on low-end devices
- Respect reduced motion preferences

## Animation Performance Monitoring

Ensuring smooth and performant animations is crucial for a good user experience. Follow these practices to monitor and optimize animation performance:

### 1. Utilize Flutter DevTools

Flutter DevTools is an invaluable tool for debugging and profiling Flutter applications, including animation performance.

- **Performance Overlay**: Enable the performance overlay in DevTools to visualize UI and GPU rasterization times. Aim for consistent 60fps (or 120fps on supported devices). Any red bars indicate dropped frames.
- **Timeline**: Use the Timeline tab to record and analyze frames. Look for expensive build, layout, paint, and raster operations that might be causing jank. Identify widgets that are rebuilding unnecessarily.
- **CPU Profiler**: Profile CPU usage during animations to pinpoint bottlenecks in your code.
- **Memory View**: Monitor memory usage to ensure animations are not causing memory leaks or excessive allocations.

### 2. Best Practices for Smooth Animations

- **Minimize Widget Rebuilds**: Animations often involve frequent widget rebuilds. Use `const` widgets where possible, and `RepaintBoundary` for static parts of an animated widget to prevent unnecessary repainting of the entire subtree.
- **Avoid Expensive Operations in Build Methods**: Keep `build` methods lean. Avoid complex calculations or heavy I/O operations within them, especially for animated widgets.
- **Use `AnimatedBuilder` and `AnimatedWidget`**: For complex animations, use `AnimatedBuilder` or `AnimatedWidget` to separate the animation logic from the widget's `build` method. This ensures that only the necessary parts of the widget tree are rebuilt when the animation value changes.
- **Optimize Image Loading**: If animating images, ensure they are loaded efficiently and are appropriately sized. Consider using `CachedNetworkImage` for network images.
- **Transformations over Layout Changes**: Prefer `Transform.scale`, `Transform.translate`, and `Transform.rotate` for animations over changes to `width`, `height`, `padding`, or `margin`. Layout changes are more expensive as they trigger a full layout pass.
- **Implicit Animations for Simplicity**: For simple property changes, `AnimatedContainer`, `AnimatedOpacity`, `AnimatedPositioned`, etc., are often more performant and easier to use than explicit `AnimationController`s.
- **Dispose of Animation Controllers**: Always `dispose()` your `AnimationController`s when the `State` object is removed from the tree to prevent memory leaks.
- **Test on Real Devices**: Always test animations on a variety of real devices, especially lower-end ones, to ensure consistent performance across the target audience.
- **Consider `TickerMode`**: If a widget subtree should not animate (e.g., when off-screen), wrap it in a `TickerMode` widget with `enabled: false` to prevent unnecessary ticker updates.

### 3. Respect Reduced Motion Settings

- **`MediaQuery.of(context).accessibleNavigation`**: Check this property to determine if the user has enabled accessibility features that might indicate a preference for reduced motion.
- **`MediaQuery.of(context).disableAnimations`**: While not a direct "reduced motion" setting, this can be used in conjunction with other checks to provide a more accessible experience.
- **Conditional Animations**: Implement logic to reduce or disable complex animations when reduced motion settings are active, providing a more comfortable experience for users with motion sensitivities.
