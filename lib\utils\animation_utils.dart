// Animation utilities for Journeyman Jobs

import 'package:flutter/material.dart';

/// AnimationDurations provides standardized durations for UI animations.
class AnimationDurations {
  static const Duration short = Duration(milliseconds: 150);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration long = Duration(milliseconds: 600);
  static const Duration veryLong = Duration(milliseconds: 1000);
}

/// AnimationCurves provides standardized curves for different interaction types.
class AnimationCurves {
  static const Curve standard = Curves.easeInOut;
  static const Curve fastIn = Curves.fastOutSlowIn;
  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;
  static const Curve decelerate = Curves.decelerate;
  static const Curve bounce = Curves.bounceOut;
  static const Curve elastic = Curves.elasticOut;
  static const Curve linear = Curves.linear;
}

/// AnimationUtils provides helper methods for common animation patterns.
class AnimationUtils {
  /// Fade transition builder for use in AnimatedSwitcher or custom transitions.
  static Widget fadeTransition(
    Widget child,
    Animation<double> animation,
  ) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// Slide transition builder for horizontal movement.
  static Widget slideTransitionHorizontal(
    Widget child,
    Animation<double> animation, {
    bool reverse = false,
    double offset = 1.0,
  }) {
    final begin = Offset(reverse ? -offset : offset, 0);
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// Slide transition builder for vertical movement.
  static Widget slideTransitionVertical(
    Widget child,
    Animation<double> animation, {
    bool reverse = false,
    double offset = 1.0,
  }) {
    final begin = Offset(0, reverse ? offset : -offset);
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// Scale transition builder.
  static Widget scaleTransition(
    Widget child,
    Animation<double> animation,
  ) {
    return ScaleTransition(
      scale: animation,
      child: child,
    );
  }
}

/// A widget that applies a subtle scale animation on tap, suitable for buttons.
class ButtonPressEffect extends StatefulWidget {
  const ButtonPressEffect({
    super.key,
    required this.child,
    this.onTap,
    this.scaleFactor = 0.95,
    this.duration = AnimationDurations.short,
    this.curve = AnimationCurves.easeOut,
  });

  final Widget child;
  final VoidCallback? onTap;
  final double scaleFactor;
  final Duration duration;
  final Curve curve;

  @override
  State<ButtonPressEffect> createState() => _ButtonPressEffectState();
}

class _ButtonPressEffectState extends State<ButtonPressEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: widget.scaleFactor).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    widget.onTap?.call();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: widget.child,
      ),
    );
  }
}

/// A widget that provides a ripple effect on tap, suitable for list items or cards.
class RippleEffect extends StatelessWidget {
  const RippleEffect({
    super.key,
    required this.child,
    this.onTap,
    this.borderRadius,
    this.splashColor,
    this.highlightColor,
  });

  final Widget child;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final Color? splashColor;
  final Color? highlightColor;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        splashColor: splashColor,
        highlightColor: highlightColor,
        child: child,
      ),
    );
  }
}

/// A widget that displays a continuous loading animation.
class LoadingAnimation extends StatefulWidget {
  const LoadingAnimation({
    super.key,
    this.size = 24.0,
    this.color,
    this.duration = const Duration(milliseconds: 800),
    this.strokeWidth = 2.0,
  });

  final double size;
  final Color? color;
  final Duration duration;
  final double strokeWidth;

  @override
  State<LoadingAnimation> createState() => _LoadingAnimationState();
}

class _LoadingAnimationState extends State<LoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: CircularProgressIndicator(
          strokeWidth: widget.strokeWidth,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.color ?? Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }
}

/// A widget that animates its child with a staggered delay, suitable for list items.
class StaggeredListAnimation extends StatefulWidget {
  final Widget child;
  final int index;
  final Duration itemDelay;
  final Duration itemDuration;
  final Curve curve;
  final Offset slideBeginOffset;

  const StaggeredListAnimation({
    super.key,
    required this.child,
    required this.index,
    this.itemDelay = const Duration(milliseconds: 100),
    this.itemDuration = AnimationDurations.medium,
    this.curve = AnimationCurves.easeOut,
    this.slideBeginOffset = const Offset(0.0, 0.2), // Slide from bottom
  });

  @override
  State<StaggeredListAnimation> createState() => _StaggeredListAnimationState();
}

class _StaggeredListAnimationState extends State<StaggeredListAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.itemDuration,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    _slideAnimation = Tween<Offset>(begin: widget.slideBeginOffset, end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    Future.delayed(widget.itemDelay * widget.index, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: widget.child,
      ),
    );
  }
}

/// A widget that applies a scale effect on hover and can indicate selection.
class HoverScaleEffect extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final bool isSelected;
  final double scaleFactor;
  final Duration duration;
  final Curve curve;
  final Color? selectedBorderColor;
  final double selectedBorderWidth;

  const HoverScaleEffect({
    super.key,
    required this.child,
    this.onTap,
    this.isSelected = false,
    this.scaleFactor = 1.05,
    this.duration = AnimationDurations.short,
    this.curve = AnimationCurves.easeOut,
    this.selectedBorderColor,
    this.selectedBorderWidth = 2.0,
  });

  @override
  State<HoverScaleEffect> createState() => _HoverScaleEffectState();
}

class _HoverScaleEffectState extends State<HoverScaleEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: widget.scaleFactor).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
  }

  @override
  void didUpdateWidget(HoverScaleEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      // Potentially trigger an animation for selection change if desired
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool hovering) {
    setState(() {
      _isHovered = hovering;
      if (_isHovered) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = widget.selectedBorderColor ?? Theme.of(context).primaryColor;
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            );
          },
          child: Container(
            decoration: widget.isSelected
                ? BoxDecoration(
                    border: Border.all(
                      color: borderColor,
                      width: widget.selectedBorderWidth,
                    ),
                    borderRadius: (widget.child is InkWell && (widget.child as InkWell).borderRadius != null)
                        ? (widget.child as InkWell).borderRadius // Attempt to match InkWell's border
                        : BorderRadius.circular(8.0), // Default if not found or not InkWell
                  )
                : null,
            child: widget.child,
          ),
        ),
      ),
    );
  }
}
