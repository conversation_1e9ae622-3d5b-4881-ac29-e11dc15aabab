import 'dart:async';
import 'dart:developer' as developer;
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

// Enum for different error types
enum ErrorType {
  network,
  firebase,
  validation,
  unknown,
}

// Base class for custom exceptions
class AppException implements Exception {
  final String message;
  final ErrorType type;
  final dynamic originalException;
  final StackTrace? stackTrace;

  AppException({
    required this.message,
    required this.type,
    this.originalException,
    this.stackTrace,
  });

  @override
  String toString() {
    return 'AppException: $message (Type: $type, OriginalException: $originalException)';
  }
}

// Specific exception types
class NetworkException extends AppException {
  NetworkException({
    required String message,
    dynamic originalException,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          type: ErrorType.network,
          originalException: originalException,
          stackTrace: stackTrace,
        );
}

class FirebaseNetworkException extends AppException {
  FirebaseNetworkException({
    required String message,
    dynamic originalException,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          type: ErrorType.firebase, // Could also be network, but more specific
          originalException: originalException,
          stackTrace: stackTrace,
        );
}

class ValidationException extends AppException {
  ValidationException({
    required String message,
    dynamic originalException,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          type: ErrorType.validation,
          originalException: originalException,
          stackTrace: stackTrace,
        );
}

class UnknownException extends AppException {
  UnknownException({
    required String message,
    dynamic originalException,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          type: ErrorType.unknown,
          originalException: originalException,
          stackTrace: stackTrace,
        );
}

// User-facing error details
class UserFriendlyError {
  final String title;
  final String message;
  final String? recoverySuggestion;

  UserFriendlyError({
    required this.title,
    required this.message,
    this.recoverySuggestion,
  });
}

// Error Handler class
class ErrorHandler {
  // Hook for remote logging integration
  static Function(AppException exception)? onRemoteLog;

  // Centralized error processing
  static UserFriendlyError handle(dynamic error, StackTrace? stackTrace) {
    AppException appException;

    if (error is AppException) {
      appException = error;
    } else if (error is FirebaseException) {
      appException = _handleFirebaseException(error, stackTrace);
    } else if (error is TimeoutException) {
      appException = NetworkException(
        message: 'The request timed out. Please try again.',
        originalException: error,
        stackTrace: stackTrace,
      );
    }
    // Add more generic error type checks here (e.g., SocketException, HttpException)
    else {
      appException = UnknownException(
        message: 'An unexpected error occurred: ${error.toString()}',
        originalException: error,
        stackTrace: stackTrace,
      );
    }

    // Log the error
    logError(appException);

    // Trigger remote logging if configured
    if (onRemoteLog != null) {
      onRemoteLog!(appException);
    }

    return _mapToUserFriendlyError(appException);
  }

  static AppException _handleFirebaseException(FirebaseException e, StackTrace? s) {
    developer.log(
      'FirebaseException caught: ${e.code}',
      name: 'ErrorHandler.Firebase',
      error: e,
      stackTrace: s,
    );
    // More specific Firebase error handling
    switch (e.code) {
      case 'unavailable':
      case 'network-request-failed':
        return FirebaseNetworkException(
            message: 'Network error: Please check your internet connection and try again.',
            originalException: e,
            stackTrace: s);
      case 'permission-denied':
        return FirebaseNetworkException(
            message: 'Permission denied. You do not have access to this resource.',
            originalException: e,
            stackTrace: s);
      // Add more Firebase specific error codes here
      default:
        return FirebaseNetworkException(
            message: e.message ?? 'An unknown Firebase error occurred.',
            originalException: e,
            stackTrace: s);
    }
  }

  // Logging utility
  static void logError(AppException exception) {
    if (kDebugMode) {
      developer.log(
        'Error: ${exception.message}',
        name: 'ErrorHandler.${exception.type.toString().split('.').last}',
        error: exception.originalException,
        stackTrace: exception.stackTrace,
      );
    }
    // In a real app, you might integrate with a logging service like Sentry, Firebase Crashlytics, etc.
  }

  // Map AppException to UserFriendlyError
  static UserFriendlyError _mapToUserFriendlyError(AppException exception) {
    String title = 'Error';
    String message = exception.message;
    String? recoverySuggestion = 'Please try again later.';

    switch (exception.type) {
      case ErrorType.network:
        title = 'Network Error';
        message = exception.message;
        recoverySuggestion = 'Please check your internet connection and try again. If the problem persists, contact support.';
        break;
      case ErrorType.firebase:
        title = 'Service Error';
        message = exception.message;
        if (exception is FirebaseNetworkException) {
             recoverySuggestion = 'Please check your internet connection. If the issue continues, the service might be temporarily unavailable.';
        } else {
            recoverySuggestion = 'There was an issue with a required service. Please try again. If the problem persists, contact support.';
        }
        break;
      case ErrorType.validation:
        title = 'Invalid Input';
        message = exception.message;
        recoverySuggestion = 'Please check the information you provided and try again.';
        break;
      case ErrorType.unknown:
        title = 'Unexpected Error';
        message = 'An unexpected error occurred. We are working to fix it.';
        recoverySuggestion = 'Please try again later. If you continue to see this message, please report the issue.';
        break;
    }
    // Customize messages further based on specific error codes or original exceptions if needed
    return UserFriendlyError(
      title: title,
      message: message,
      recoverySuggestion: recoverySuggestion,
    );
  }

  // Retry utility with exponential backoff
  static Future<T> retry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffFactor = 2.0,
    bool Function(Exception e)? retryIf,
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;
        if (attempt >= maxRetries || (e is Exception && retryIf != null && !retryIf(e))) {
          rethrow;
        }

        // Log retry attempt
        if (kDebugMode) {
          developer.log(
            'Retry attempt $attempt/$maxRetries for operation after error: $e. Retrying in $delay...',
            name: 'ErrorHandler.Retry',
          );
        }
        
        await Future.delayed(delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * backoffFactor).round());
      }
    }
    // Should not be reached if maxRetries > 0, but as a fallback:
    throw UnknownException(message: "Operation failed after $maxRetries retries.");
  }

  // Wrapper for Firebase operations
  static Future<T> handleFirebaseCall<T>(
      Future<T> Function() firebaseCall,
      {String? operationName} // Optional: for more specific logging/messaging
  ) async {
    try {
      // Could use retry here if desired for specific Firebase calls known to be flaky
      // For example: return await retry(() => firebaseCall());
      return await firebaseCall();
    } catch (e, s) {
      final operationDesc = operationName ?? 'Firebase operation';
      developer.log(
        'Error during $operationDesc',
        name: 'ErrorHandler.FirebaseCall',
        error: e,
        stackTrace: s,
      );
      // The handle method will further process and log this
      throw handle(e, s); // Rethrow as UserFriendlyError or allow handle to wrap it
    }
  }

  // Wrapper for generic network operations
  static Future<T> handleNetworkCall<T>(
    Future<T> Function() networkCall,
    {String? operationName} // Optional: for more specific logging/messaging
  ) async {
    try {
      // Example of using retry for all network calls
      return await retry(networkCall, retryIf: (e) => e is NetworkException || e is TimeoutException || (e is FirebaseException && (e.code == 'unavailable' || e.code == 'network-request-failed')));
    } catch (e, s) {
      final operationDesc = operationName ?? 'Network operation';
       developer.log(
        'Error during $operationDesc',
        name: 'ErrorHandler.NetworkCall',
        error: e,
        stackTrace: s,
      );
      // The handle method will further process and log this
      throw handle(e, s); // Rethrow as UserFriendlyError or allow handle to wrap it
    }
  }
}

// Example of how UI can use this (conceptual, not to be placed in UI code directly by this mode)
/*
void showErrorDialog(BuildContext context, UserFriendlyError error) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(error.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.message),
            if (error.recoverySuggestion != null) ...[
              SizedBox(height: 8),
              Text(error.recoverySuggestion!, style: TextStyle(fontStyle: FontStyle.italic)),
            ]
          ],
        ),
        actions: <Widget>[
          TextButton(
            child: Text('OK'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}

// Example usage in a Flutter widget or service:
Future<void> fetchData(BuildContext context) async {
  try {
    // final data = await SomeService.fetchDataFromApi();
    // OR
    // final data = await ErrorHandler.handleNetworkCall(() => http.get(Uri.parse('...'));
    // OR
    // final user = await ErrorHandler.handleFirebaseCall(() => FirebaseAuth.instance.signInAnonymously());
    // updateUI(data);
  } catch (e) { // This will catch the UserFriendlyError rethrown by handleNetworkCall/handleFirebaseCall
    if (e is UserFriendlyError) {
       // showErrorDialog(context, e);
    } else {
      // Fallback for unexpected errors not caught by ErrorHandler.handle
      // final genericError = ErrorHandler.handle(e, StackTrace.current);
      // showErrorDialog(context, genericError);
    }
  }
}
*/