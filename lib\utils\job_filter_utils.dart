import 'package:cloud_firestore/cloud_firestore.dart';
import '../backend/schema/jobs_record.dart';
import '../backend/schema/users_record.dart';
import '../backend/schema/enums/enums.dart';

// TODO: Consider a more robust caching solution if needed (e.g., Hive, shared_preferences for persistence)
// TODO: Cache invalidation strategy for paginated results needs review.
final Map<String, PaginatedJobsResult> _jobCache = {};

class PaginatedJobsResult {
  final List<JobsRecord> jobs;
  final DocumentSnapshot? lastDocument; // Last document of the current page, for next query
  final bool hasMore;

  PaginatedJobsResult({required this.jobs, this.lastDocument, required this.hasMore});
}

class JobFilterUtils {
  /// Builds a Firestore query to filter jobs on the server side.
  ///
  /// [user]: The current user, used for some default filters or preferences.
  /// [filters]: A map of additional filters applied by the user from the UI.
  ///   Expected keys: 'jobType' (String), 'location' (String), 'classification' (String), etc.
  Query<JobsRecord> getFilteredJobsQuery(
    UsersRecord user,
    Map<String, dynamic> filters, {
    int? limit,
    DocumentSnapshot? startAfterDoc,
  }) {
    Query<JobsRecord> query = JobsRecord.collection.withConverter<JobsRecord>(
        fromFirestore: (DocumentSnapshot<Map<String, dynamic>> snapshot, SnapshotOptions? options) =>
            JobsRecord.fromSnapshot(snapshot),
        toFirestore: (JobsRecord job, SetOptions? options) => job.toMap());

    // Filter by job type (classification from JobsRecord)
    if (filters.containsKey('jobType') && filters['jobType'] != null) {
      query = query.where('classification', isEqualTo: filters['jobType']);
    }

    // Filter by location
    if (filters.containsKey('location') && filters['location'] != null) {
      query = query.where('location', isEqualTo: filters['location']);
    }

    // Filter by specific job classification (e.g., from JobsRecord.jobClass)
    if (filters.containsKey('jobClass') && filters['jobClass'] != null) {
      query = query.where('jobClass', isEqualTo: filters['jobClass']);
    }

    // Filter by local number
    if (filters.containsKey('localNumber') && filters['localNumber'] != null) {
      query = query.where('localNumber', isEqualTo: filters['localNumber']);
    }
    
    // Add more server-side filters as needed based on JobsRecord fields

    // Default ordering (e.g., by timestamp descending)
    // This orderBy must be consistent for pagination to work correctly.
    // If multiple orderBy clauses are used, ensure the last one is on a unique field or use a tie-breaker.
    query = query.orderBy('timestamp', descending: true);
    // It's good practice to add .orderBy(FieldPath.documentId) as a final tie-breaker for consistent ordering
    // if 'timestamp' can have duplicate values, though Firestore often handles this.
    // query = query.orderBy(FieldPath.documentId, descending: true); // Or ascending, matching timestamp

    if (startAfterDoc != null) {
      query = query.startAfterDocument(startAfterDoc);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query;
  }

  /// Sorts job listings based on user preferences.
  List<JobsRecord> sortJobsByPreference(
      List<JobsRecord> jobs, UsersRecord user) {
    jobs.sort((a, b) {
      int scoreA = _calculatePreferenceScore(a, user);
      int scoreB = _calculatePreferenceScore(b, user);
      return scoreB.compareTo(scoreA); // Higher score first
    });
    return jobs;
  }

  int _calculatePreferenceScore(JobsRecord job, UsersRecord user) {
    int score = 0;

    // Prioritize preferred locals
    if (user.hasPreferredLocal1() && job.localNumber.toString() == user.preferredLocal1) {
      score += 100;
    } else if (user.hasPreferredLocal2() && job.localNumber.toString() == user.preferredLocal2) {
      score += 80;
    } else if (user.hasPreferredLocal3() && job.localNumber.toString() == user.preferredLocal3) {
      score += 60;
    }

    // Match user's classification
    if (user.hasClassification() && job.classification.toLowerCase().contains(user.classification!.serialize().toLowerCase())) {
        score += 50;
    }
    
    // Match construction types (assuming job.jobClass or job.jobDescription might contain this info)
    // This is a simplified match; ideally, jobs would have a dedicated 'constructionTypes' field.
    if (user.hasConstructionTypes()) {
        for (String type in user.constructionTypes) {
            if (job.jobClass.toLowerCase().contains(type.toLowerCase()) || 
                (job.hasJobDescription() && job.jobDescription.toLowerCase().contains(type.toLowerCase()))) {
                score += 10;
            }
        }
    }

    // Consider user's career goals/motivations (these are boolean flags in UsersRecord)
    // This is highly subjective and needs business logic definition.
    // Example: if user wants 'higherPayRate' and job has high wage (needs wage parsing).
    // For now, just a placeholder.
    if (user.hasHigherPayRate() /* && isHighWage(job.wage) */) score += 5;
    if (user.hasLearnNewSkill() /* && jobOffersSkillDevelopment(job) */) score += 5;
    if (user.hasTravelToNewLocation() && job.location.toLowerCase() != user.city.toLowerCase()) score += 5; // Simple check
    if (user.hasFindLongTermWork() /* && job.isLongTerm() */) score += 5;


    // Recency of posting (newer jobs get higher score)
    if (job.hasTimestamp()) {
        final now = DateTime.now();
        final difference = now.difference(job.timestamp!);
        if (difference.inDays < 7) score += 20;
        else if (difference.inDays < 30) score += 10;
    }

    return score;
  }

  /// Filters out jobs for which the user is not qualified.
  /// This is a client-side filter after initial data fetch.
  List<JobsRecord> filterJobsByQualifications(
      List<JobsRecord> jobs, UsersRecord user) {
    return jobs.where((job) {
      // Basic classification match
      if (user.hasClassification()) {
        // Assuming job.classification is a string that might contain the user's classification
        // Or job.qualifications string needs parsing.
        // For simplicity, let's check if job.classification matches user.classification enum.
        // This might need refinement based on actual data in job.classification vs job.qualifications.
        final userClassificationStr = user.classification!.serialize().toLowerCase();
        bool classificationMatch = job.classification.toLowerCase().contains(userClassificationStr);

        if (job.hasQualifications()) {
             // Example: "Journeyman Wireman, OSHA 10, CDL A"
            List<String> requiredQualifications = job.qualifications.split(',').map((q) => q.trim().toLowerCase()).toList();
            if (requiredQualifications.contains(userClassificationStr)) {
                classificationMatch = true;
            }
            // TODO: Add more sophisticated qualification checking logic here.
            // e.g., check for specific certifications from user profile against job.qualifications
            // For example, if user has a list of certifications:
            // bool hasAllRequiredCerts = requiredQualifications.every((req) => user.certifications.contains(req));
            // if (!hasAllRequiredCerts) return false;
        }
         if (!classificationMatch) return false; // If no form of classification matches, filter out.
      }


      // Ticket number check (if applicable to job type)
      // This is a placeholder; actual logic depends on how ticket numbers relate to job qualifications.
      // if (user.hasTicketNumber() && jobRequiresTicket(job) && !job.qualifications.contains(user.ticketNumber.toString())) {
      //   return false;
      // }
      
      // TODO: Implement more detailed qualification checks based on job.qualifications string
      // and user's profile (e.g., certifications, experience years if available).
      // For now, this is a basic filter.
      return true; 
    }).toList();
  }

  /// Retrieves cached filtered job results.
  PaginatedJobsResult? getCachedFilteredJobs(String cacheKey) {
    if (_jobCache.containsKey(cacheKey)) {
      print('Cache hit for key: $cacheKey');
      // Return a new instance to prevent modification of cached object if it's mutable,
      // though PaginatedJobsResult is simple here.
      final cached = _jobCache[cacheKey]!;
      return PaginatedJobsResult(jobs: List<JobsRecord>.from(cached.jobs), lastDocument: cached.lastDocument, hasMore: cached.hasMore);
    }
    print('Cache miss for key: $cacheKey');
    return null;
  }

  /// Caches filtered job results.
  void cacheFilteredJobs(String cacheKey, PaginatedJobsResult result) {
    print('Caching ${result.jobs.length} jobs for key: $cacheKey. HasMore: ${result.hasMore}');
    _jobCache[cacheKey] = result; // Store the paginated result
  }

  /// Clears the entire job cache or a specific entry.
  void clearCache({String? cacheKey}) {
    if (cacheKey != null) {
      _jobCache.remove(cacheKey);
      print('Cache cleared for key: $cacheKey');
    } else {
      _jobCache.clear();
      print('Entire job cache cleared');
    }
  }

  /// Example API for UI: Fetches jobs, filters, sorts, and caches.
  ///
  /// [user]: The current user.
  /// [currentFilters]: Filters applied from the UI (e.g., job type, location).
  /// [forceRefresh]: If true, bypasses cache and fetches fresh data.
  Future<PaginatedJobsResult> fetchAndProcessJobs({
    required UsersRecord user,
    Map<String, dynamic> currentFilters = const {},
    bool forceRefresh = false,
    int? pageLimit, // Number of items per page
    DocumentSnapshot? lastFetchedDoc, // Last document from the previous page
  }) async {
    // Generate a cache key based on user ID, filters, and pagination state
    String pageKey = lastFetchedDoc?.id ?? 'firstPage';
    String cacheKey = 'user_${user.uid}_filters_${currentFilters.hashCode}_page_${pageKey}_limit_${pageLimit ?? 'all'}';

    if (!forceRefresh) {
      final cachedResult = getCachedFilteredJobs(cacheKey);
      if (cachedResult != null) {
        return cachedResult;
      }
    }

    // 1. Get Firestore query with server-side filters and pagination
    Query<JobsRecord> query = getFilteredJobsQuery(
      user,
      currentFilters,
      limit: pageLimit != null ? pageLimit + 1 : null, // Fetch one extra to check if there's a next page
      startAfterDoc: lastFetchedDoc,
    );

    // 2. Execute query
    QuerySnapshot<JobsRecord> querySnapshot = await query.get();
    List<JobsRecord> fetchedJobs = querySnapshot.docs.map((doc) => doc.data()).toList();
    
    bool hasMore = false;
    if (pageLimit != null && fetchedJobs.length > pageLimit) {
      hasMore = true;
      fetchedJobs.removeLast(); // Remove the extra item
    }

    DocumentSnapshot? newLastFetchedDoc = querySnapshot.docs.isNotEmpty && fetchedJobs.isNotEmpty
        ? querySnapshot.docs[fetchedJobs.length -1] // Get the actual last document of the current page
        : null;


    // 3. Apply client-side qualification filtering (if still necessary after server-side filters)
    // Consider if these can be moved to server-side for better performance
    List<JobsRecord> qualifiedJobs =
        filterJobsByQualifications(fetchedJobs, user);

    // 4. Apply client-side preference-based sorting (if still necessary)
    // Sorting should ideally be done server-side via orderBy for consistency with pagination.
    // If client-side sorting is essential, it should be applied carefully with pagination.
    // For now, keeping it, but this is an area for further optimization.
    List<JobsRecord> sortedJobs = sortJobsByPreference(qualifiedJobs, user);
    
    PaginatedJobsResult result = PaginatedJobsResult(
      jobs: sortedJobs,
      lastDocument: newLastFetchedDoc,
      hasMore: hasMore
    );

    // 5. Cache results
    cacheFilteredJobs(cacheKey, result);

    return result;
  }

  // --- Helper methods for qualification parsing (examples, need refinement) ---

  // bool jobRequiresTicket(JobsRecord job) {
  //   // Placeholder: Determine if a job type typically requires a ticket number
  //   // e.g., based on job.classification or job.jobClass
  //   return job.classification.toLowerCase().contains('lineman'); 
  // }

  // bool isHighWage(String wageString) {
  //   // Placeholder: Parse wage string (e.g., "$50/hr", "DOE") and determine if high
  //   // This requires robust parsing logic.
  //   try {
  //     final numericPart = wageString.replaceAll(RegExp(r'[^0-9.]'), '');
  //     if (numericPart.isNotEmpty) {
  //       return double.parse(numericPart) > 40; // Example threshold
  //     }
  //   } catch (e) {
  //     // Ignore parsing errors
  //   }
  //   return false;
  // }
}

// Example Usage (conceptual, would be in a FlutterFlow custom action or widget state)
/*
Future<void> loadJobs(UsersRecord currentUser, Map<String, dynamic> uiFilters) async {
  JobFilterUtils filterUtils = JobFilterUtils();
  
  // Set loading state true
  
  try {
    List<JobsRecord> jobs = await filterUtils.fetchAndProcessJobs(
      user: currentUser,
      currentFilters: uiFilters,
      forceRefresh: false, // Set to true if user pulls to refresh
    );
    
    // Update UI with jobs
    // Set loading state false
    
  } catch (e) {
    // Handle error
    // Set loading state false
    print('Error fetching or processing jobs: $e');
  }
}
*/