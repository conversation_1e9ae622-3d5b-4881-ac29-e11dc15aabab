import 'package:flutter/material.dart';
import 'package:journeyman_jobs/flutter_flow/flutter_flow_theme.dart';

class ErrorDisplayWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final double iconSize;
  final Color? iconColor;

  const ErrorDisplayWidget({
    Key? key,
    this.title = 'An Error Occurred',
    this.message = 'Please try again later.',
    this.icon = Icons.error_outline,
    this.iconSize = 48.0,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: iconColor ?? FlutterFlowTheme.of(context).error,
          ),
          SizedBox(height: 16.0),
          Text(
            title,
            style: FlutterFlowTheme.of(context).bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.0),
          Text(
            message,
            style: FlutterFlowTheme.of(context).bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}