import 'package:flutter/material.dart';
import 'package:journeyman_jobs/flutter_flow/flutter_flow_theme.dart';

class LoadingIndicatorWidget extends StatelessWidget {
  final double size;
  final Color? color;

  const LoadingIndicatorWidget({
    Key? key,
    this.size = 50.0,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? FlutterFlowTheme.of(context).primary,
          ),
        ),
      ),
    );
  }
}