import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// A reusable pop-up widget that fetches and displays a Firestore document's fields.
/// 
/// Example usage:
/// ```dart
/// showDialog(
///   context: context,
///   builder: (context) => FirestorePopUp(
///     collection: 'jobs',
///     documentId: 'job123',
///     fieldsToDisplay: ['title', 'description', 'location'],
///     fieldLabels: {'title': 'Job Title', 'description': 'Description', 'location': 'Location'},
///     fieldOrder: ['title', 'location', 'description'],
///     backgroundColor: Colors.white,
///     padding: EdgeInsets.all(24),
///     borderRadius: BorderRadius.circular(20),
///   ),
/// );
/// ```
class FirestorePopUp extends StatelessWidget {
  /// The Firestore collection name.
  final String collection;

  /// The document ID within the collection.
  final String documentId;

  /// Optional: List of field keys to display. If null, all fields are shown.
  final List<String>? fieldsToDisplay;

  /// Optional: Map of field keys to custom labels.
  final Map<String, String>? fieldLabels;

  /// Optional: List specifying the order of fields.
  final List<String>? fieldOrder;

  /// Optional: Background color of the pop-up.
  final Color? backgroundColor;

  /// Optional: Padding inside the pop-up.
  final EdgeInsetsGeometry? padding;

  /// Optional: Border radius of the pop-up.
  final BorderRadiusGeometry? borderRadius;

  /// Optional: Text style for field labels.
  final TextStyle? labelTextStyle;

  /// Optional: Text style for field values.
  final TextStyle? valueTextStyle;

  const FirestorePopUp({
    Key? key,
    required this.collection,
    required this.documentId,
    this.fieldsToDisplay,
    this.fieldLabels,
    this.fieldOrder,
    this.backgroundColor,
    this.padding,
    this.borderRadius,
    this.labelTextStyle,
    this.valueTextStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
        ),
        padding: padding ?? const EdgeInsets.all(20),
        constraints: const BoxConstraints(
          minWidth: 280,
          maxWidth: 400,
          minHeight: 100,
          maxHeight: 500,
        ),
        child: FutureBuilder<DocumentSnapshot<Map<String, dynamic>>>(
          future: FirebaseFirestore.instance
              .collection(collection)
              .doc(documentId)
              .get(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }
            if (snapshot.hasError) {
              return Center(
                child: Text(
                  'Error loading data.',
                  style: TextStyle(color: Colors.red[700]),
                ),
              );
            }
            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(
                child: Text('Document not found.'),
              );
            }

            final data = snapshot.data!.data()!;
            // Determine which fields to show
            List<String> keys;
            if (fieldsToDisplay != null) {
              keys = fieldsToDisplay!
                  .where((k) => data.containsKey(k))
                  .toList();
            } else {
              keys = data.keys.toList();
            }

            // Apply custom order if provided
            if (fieldOrder != null) {
              keys.sort((a, b) {
                int ia = fieldOrder!.indexOf(a);
                int ib = fieldOrder!.indexOf(b);
                if (ia == -1 && ib == -1) return 0;
                if (ia == -1) return 1;
                if (ib == -1) return -1;
                return ia.compareTo(ib);
              });
            }

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Close button (top right)
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Scrollbar(
                    thumbVisibility: true,
                    child: ListView.separated(
                      itemCount: keys.length,
                      separatorBuilder: (_, __) => const Divider(height: 16),
                      itemBuilder: (context, index) {
                        final key = keys[index];
                        final label = fieldLabels != null && fieldLabels!.containsKey(key)
                            ? fieldLabels![key]!
                            : key;
                        final value = data[key];
                        return _FieldRow(
                          label: label,
                          value: value,
                          labelTextStyle: labelTextStyle,
                          valueTextStyle: valueTextStyle,
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _FieldRow extends StatelessWidget {
  final String label;
  final dynamic value;
  final TextStyle? labelTextStyle;
  final TextStyle? valueTextStyle;

  const _FieldRow({
    Key? key,
    required this.label,
    required this.value,
    this.labelTextStyle,
    this.valueTextStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: labelTextStyle ??
              Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(fontWeight: FontWeight.bold),
        ),
        Expanded(
          child: Text(
            _formatValue(value),
            style: valueTextStyle ?? Theme.of(context).textTheme.titleMedium,
          ),
        ),
      ],
    );
  }

  String _formatValue(dynamic value) {
    if (value == null) return '';
    if (value is DateTime) return value.toIso8601String();
    if (value is Timestamp) return value.toDate().toIso8601String();
    if (value is List || value is Map) return value.toString();
    return value.toString();
  }
}